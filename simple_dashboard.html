<!DOCTYPE html>
<html>
<head>
    <title>ReaxFF优化监控 (简化版)</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #1a1a1a; color: white; }
        .container { max-width: 1200px; margin: 0 auto; }
        .metrics { display: grid; grid-template-columns: repeat(4, 1fr); gap: 20px; margin: 20px 0; }
        .metric-card { background: #2a2a2a; padding: 20px; border-radius: 8px; text-align: center; }
        .metric-value { font-size: 2em; font-weight: bold; color: #4CAF50; }
        .status { background: #2a2a2a; padding: 20px; border-radius: 8px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>ReaxFF优化监控仪表板 (简化版)</h1>
        
        <div class="metrics">
            <div class="metric-card">
                <h3>当前迭代</h3>
                <div class="metric-value" id="iteration">0</div>
            </div>
            <div class="metric-card">
                <h3>当前损失</h3>
                <div class="metric-value" id="loss">-</div>
            </div>
            <div class="metric-card">
                <h3>最佳损失</h3>
                <div class="metric-value" id="best-loss">-</div>
            </div>
            <div class="metric-card">
                <h3>状态</h3>
                <div class="metric-value" id="status">待机</div>
            </div>
        </div>
        
        <div class="status">
            <h3>📊 监控状态</h3>
            <p>✅ 简化版监控仪表板已启动</p>
            <p>⚠️ 完整功能需要安装: flask flask-socketio plotly</p>
            <p>💡 运行 <code>pip install flask flask-socketio plotly</code> 安装完整依赖</p>
        </div>
    </div>
    
    <script>
        // 简单的状态更新
        let iteration = 0;
        setInterval(() => {
            iteration++;
            document.getElementById('iteration').textContent = iteration;
            document.getElementById('status').textContent = '运行中';
        }, 1000);
    </script>
</body>
</html>