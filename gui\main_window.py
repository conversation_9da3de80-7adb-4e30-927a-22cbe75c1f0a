"""
ReaxFFOpt GUI主窗口
"""

import sys
import os
import json
import numpy as np
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                            QTabWidget, QAction, QMenu, QToolBar, QStatusBar, QLabel,
                            QFileDialog, QMessageBox, QDockWidget, QSplitter, QFrame, QTableWidgetItem, QCheckBox,
                            QDialog, QListWidget, QListWidgetItem, QPushButton, QProgressDialog, QComboBox, QSpinBox, QFormLayout,
                            QInputDialog, QHeaderView)
from PyQt5.QtCore import Qt, pyqtSlot, QThreadPool, QTimer, QThread, QSettings, pyqtSignal
from PyQt5.QtGui import QIcon, QFont, QColor, QPixmap

# 导入自定义面板
from gui.parameter_panel import ParameterPanel
# 临时禁用matplotlib相关面板以解决NumPy兼容性问题
try:
    from gui.visualization_panel import VisualizationPanel
    VISUALIZATION_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ 可视化面板导入失败: {e}")
    VISUALIZATION_AVAILABLE = False
    VisualizationPanel = None

try:
    from gui.molecular_viewer import MolecularViewer
    MOLECULAR_VIEWER_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ 分子查看器导入失败: {e}")
    MOLECULAR_VIEWER_AVAILABLE = False
    MolecularViewer = None

try:
    from gui.reaction_path_panel import ReactionPathPanel
    REACTION_PATH_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ 反应路径面板导入失败: {e}")
    REACTION_PATH_AVAILABLE = False
    ReactionPathPanel = None
# 注释掉暂时不存在的导入
# from gui.data_panel import DataPanel
# from optimizer.multi_objective import MultiObjectiveOptimizer
# from calculator.reaxff_calculator import ReaxFFCalculator
from data.data_handler import DatasetWorker as DataWorker


class OptimizationWorker(QThread):
    """优化线程，用于在后台执行优化任务"""

    # 信号定义
    progress_updated = pyqtSignal(int, str)
    optimization_finished = pyqtSignal(list)
    error_occurred = pyqtSignal(str)

    def __init__(self, optimizer, initial_params, training_data):
        """初始化优化线程

        Args:
            optimizer: 优化器对象
            initial_params (dict): 初始参数
            training_data (dict): 训练数据
        """
        super().__init__()
        self.optimizer = optimizer
        self.initial_params = initial_params
        self.training_data = training_data
        self.is_running = False

    def run(self):
        """执行优化任务"""
        self.is_running = True
        try:
            # 报告开始
            self.progress_updated.emit(0, "开始优化...")

            # 模拟优化过程
            import time
            for i in range(1, 101):
                if not self.is_running:
                    break
                time.sleep(0.05)
                self.progress_updated.emit(i, f"优化进度: {i}%")

            # 报告完成
            self.progress_updated.emit(100, "优化完成")
            self.optimization_finished.emit([])

        except Exception as e:
            self.error_occurred.emit(f"优化过程中出错: {str(e)}")

        finally:
            self.is_running = False

    def stop(self):
        """停止优化"""
        self.is_running = False


class MainWindow(QMainWindow):
    """主窗口类"""

    def __init__(self):
        super().__init__()

        # 设置基本属性
        self.setWindowTitle("ReaxFFOpt - 先进的反应力场参数优化框架")
        self.setMinimumSize(1200, 800)

        # 初始化线程池
        self.thread_pool = QThreadPool()

        # 初始化ReaxFF计算器和数据
        # self.calculator = ReaxFFCalculator()  # 暂时注释掉
        self.calculator = None
        self.training_data = {}
        self.current_optimizer = None
        self.optimization_worker = None
        self.dataset_handler = None  # 数据集处理器

        # 创建中央窗口部件
        self.init_central_widget()

        # 创建菜单和工具栏
        self.create_actions()
        self.create_menus()
        self.create_toolbars()

        # 创建状态栏
        self.status_bar = self.statusBar()
        self.status_bar.showMessage("就绪")

        # 添加进度指示器
        self.progress_label = QLabel("进度: 0%")
        self.status_bar.addPermanentWidget(self.progress_label)

        # 初始化模拟优化定时器
        self.sim_timer = QTimer()
        self.sim_timer.timeout.connect(self.simulate_optimization_update)
        self.sim_iteration = 0

        # 检查是否存在默认数据集（但不自动导入）
        self.check_default_datasets()

    def init_central_widget(self):
        """初始化中央部件"""
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主分割器
        main_splitter = QSplitter(Qt.Horizontal)

        # 创建左侧选项卡面板
        left_tab_widget = QTabWidget()
        
        # 参数面板
        self.parameter_panel = ParameterPanel()
        left_tab_widget.addTab(self.parameter_panel, "参数优化")
        
        # 反应路径面板
        if REACTION_PATH_AVAILABLE and ReactionPathPanel:
            self.reaction_path_panel = ReactionPathPanel()
            left_tab_widget.addTab(self.reaction_path_panel, "反应路径")
        else:
            print("⚠️ 反应路径面板不可用，跳过创建")

        # 创建统一的可视化面板 - 使用增强可视化面板
        try:
            from gui.enhanced_visualization_panel import EnhancedVisualizationPanel
            # 使用增强可视化面板作为主要可视化面板
            self.visualization_panel = EnhancedVisualizationPanel()
            self.enhanced_viz_panel = self.visualization_panel  # 兼容性别名
            
            print(" 使用增强可视化面板作为主要可视化面板")
        except ImportError as e:
            print(f" 增强可视化模块导入失败，使用基础面板: {e}")
            # 如果增强面板不可用，使用基础面板
            if VISUALIZATION_AVAILABLE and VisualizationPanel:
                self.visualization_panel = VisualizationPanel()
            else:
                print("⚠️ 基础可视化面板也不可用，创建占位符")
                self.visualization_panel = None

        # 创建增强可视化集成面板（包含学术功能）
        try:
            from gui.enhanced_visualization_integration import EnhancedVisualizationIntegrationPanel
            self.enhanced_integration_panel = EnhancedVisualizationIntegrationPanel()
            print(" 增强可视化集成面板创建成功")
            print(" 学术功能已集成到可视化面板中")
        except Exception as e:
            print(f" 增强可视化集成面板创建失败: {e}")
            self.enhanced_integration_panel = None

        # 连接信号
        self.parameter_panel.optimization_started.connect(self.start_optimization)
        self.parameter_panel.stop_button.clicked.connect(self.stop_optimization)
        
        # 连接可视化面板的数据集选择变化信号
        if hasattr(self.visualization_panel, 'dataset_selection_changed'):
            self.visualization_panel.dataset_selection_changed.connect(self.on_visualization_dataset_selection_changed)
            print(" 已连接可视化面板数据集选择变化信号")
        
        # 创建可视化选项卡
        self.visualization_tab_widget = QTabWidget()
        
        # 添加基础可视化面板
        self.visualization_tab_widget.addTab(self.visualization_panel, "📊 基础可视化")
        
        # 添加增强可视化集成面板
        if self.enhanced_integration_panel:
            self.visualization_tab_widget.addTab(self.enhanced_integration_panel, "🎨 学术可视化 & 力场生成")
        
        # 添加到分割器 - 简化布局
        main_splitter.addWidget(left_tab_widget)
        main_splitter.addWidget(self.visualization_tab_widget)

        # 设置分割器初始大小 - 根据用户反馈调整比例
        # 左侧参数面板：右侧可视化面板 = 6:4 的比例更合理
        main_splitter.setSizes([600, 400])

        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.addWidget(main_splitter)

    def create_actions(self):
        """创建动作"""
        # 文件动作
        self.new_project_act = QAction("新建项目", self)
        self.new_project_act.setShortcut("Ctrl+N")
        self.new_project_act.setStatusTip("创建新项目")
        self.new_project_act.triggered.connect(self.new_project)

        self.open_project_act = QAction("打开项目", self)
        self.open_project_act.setShortcut("Ctrl+O")
        self.open_project_act.setStatusTip("打开现有项目")
        self.open_project_act.triggered.connect(self.open_project)

        self.save_project_act = QAction("保存项目", self)
        self.save_project_act.setShortcut("Ctrl+S")
        self.save_project_act.setStatusTip("保存当前项目")
        self.save_project_act.triggered.connect(self.save_project)

        self.exit_act = QAction("退出", self)
        self.exit_act.setShortcut("Ctrl+Q")
        self.exit_act.setStatusTip("退出应用程序")
        self.exit_act.triggered.connect(self.close)

        # 导入动作
        self.quick_import_act = QAction("快速导入默认数据集", self)
        self.quick_import_act.setStatusTip("快速导入预设的默认数据集")
        self.quick_import_act.triggered.connect(self.quick_import_default_datasets)

        self.import_ffield_act = QAction("导入力场文件", self)
        self.import_ffield_act.setStatusTip("导入ReaxFF力场文件")
        self.import_ffield_act.triggered.connect(self.import_ffield)

        self.import_structure_act = QAction("导入分子结构", self)
        self.import_structure_act.setStatusTip("导入分子结构文件")
        self.import_structure_act.triggered.connect(self.import_structure)

        self.import_training_set_act = QAction("导入训练集", self)
        self.import_training_set_act.setStatusTip("导入训练集文件")
        self.import_training_set_act.triggered.connect(self.import_training_set)

        self.import_dataset_folder_act = QAction("导入数据集文件夹", self)
        self.import_dataset_folder_act.setStatusTip("导入数据集文件夹")
        self.import_dataset_folder_act.triggered.connect(self.import_dataset_folder)

        # 优化动作
        self.start_opt_act = QAction("开始优化", self)
        self.start_opt_act.setShortcut("F5")
        self.start_opt_act.setStatusTip("开始参数优化")
        # 延迟连接，因为parameter_panel可能还没有创建
        self.start_opt_act.triggered.connect(self.start_optimization_from_menu)

        self.stop_opt_act = QAction("停止优化", self)
        self.stop_opt_act.setShortcut("F6")
        self.stop_opt_act.setStatusTip("停止当前优化")
        self.stop_opt_act.triggered.connect(self.stop_optimization)
        self.stop_opt_act.setEnabled(False)

        # 工具动作
        self.parameter_sensitivity_act = QAction("参数敏感性分析", self)
        self.parameter_sensitivity_act.setStatusTip("分析参数敏感性")
        self.parameter_sensitivity_act.triggered.connect(self.run_sensitivity_analysis)

        self.generate_ai_parameters_act = QAction("AI参数生成", self)
        self.generate_ai_parameters_act.setStatusTip("使用AI生成优化参数")
        self.generate_ai_parameters_act.triggered.connect(self.run_ai_generation)

        self.quantum_optimization_act = QAction("量子优化模式", self)
        self.quantum_optimization_act.setStatusTip("切换量子优化模式")
        self.quantum_optimization_act.triggered.connect(self.toggle_quantum_optimization_mode)



        # 多目标优化动作
        self.multi_objective_opt_act = QAction("多目标优化", self)
        self.multi_objective_opt_act.setStatusTip("运行多目标优化")
        self.multi_objective_opt_act.triggered.connect(self.run_multi_objective_optimization)

        self.multi_fidelity_opt_act = QAction("多保真度优化", self)
        self.multi_fidelity_opt_act.setStatusTip("运行多保真度优化")
        self.multi_fidelity_opt_act.triggered.connect(self.run_multi_fidelity_optimization)

        self.plot_pareto_front_act = QAction("绘制帕累托前沿", self)
        self.plot_pareto_front_act.setStatusTip("绘制多目标优化的帕累托前沿")
        self.plot_pareto_front_act.triggered.connect(self.plot_pareto_front)

        # 帮助动作
        self.about_act = QAction("关于", self)
        self.about_act.setStatusTip("显示关于信息")
        self.about_act.triggered.connect(self.show_about)

        self.help_act = QAction("帮助", self)
        self.help_act.setStatusTip("显示帮助信息")
        self.help_act.triggered.connect(self.show_help)

    def create_menus(self):
        """创建菜单"""
        # 文件菜单
        self.file_menu = self.menuBar().addMenu("文件")
        self.file_menu.addAction(self.new_project_act)
        self.file_menu.addAction(self.open_project_act)
        self.file_menu.addAction(self.save_project_act)
        self.file_menu.addSeparator()

        # 导入子菜单
        self.import_menu = self.file_menu.addMenu("导入")
        self.import_menu.addAction(self.quick_import_act)
        self.import_menu.addSeparator()
        self.import_menu.addAction(self.import_ffield_act)
        self.import_menu.addAction(self.import_structure_act)
        self.import_menu.addAction(self.import_training_set_act)
        self.import_menu.addAction(self.import_dataset_folder_act)

        # 添加保存菜单
        self.file_menu.addSeparator()  # 添加分隔线
        self.save_menu = self.file_menu.addMenu("保存")

        self.save_ffield = QAction("保存力场文件...", self)
        self.save_ffield.setShortcut("Ctrl+S")
        self.save_ffield.setStatusTip("将优化后的力场文件保存到指定位置")
        self.save_ffield.triggered.connect(self.save_force_field)
        self.save_menu.addAction(self.save_ffield)

        # 添加退出动作
        self.file_menu.addSeparator()
        self.file_menu.addAction(self.exit_act)

        # 优化菜单
        self.optimization_menu = self.menuBar().addMenu("优化")
        self.optimization_menu.addAction(self.start_opt_act)
        self.optimization_menu.addAction(self.stop_opt_act)
        self.optimization_menu.addSeparator()

        # 新增：多目标优化子菜单
        self.multi_opt_menu = self.optimization_menu.addMenu("多目标优化")
        self.multi_opt_menu.addAction(self.multi_objective_opt_act)
        self.multi_opt_menu.addAction(self.multi_fidelity_opt_act)
        self.multi_opt_menu.addSeparator()
        self.multi_opt_menu.addAction(self.plot_pareto_front_act)

        # 工具菜单
        self.tools_menu = self.menuBar().addMenu("工具")
        self.tools_menu.addAction(self.parameter_sensitivity_act)
        self.tools_menu.addAction(self.generate_ai_parameters_act)
        self.tools_menu.addSeparator()  # 添加分隔线
        self.tools_menu.addAction(self.quantum_optimization_act)  # 移到工具菜单

        # 🎓 添加学术功能
        self.tools_menu.addSeparator()
        self.academic_visualization_act = QAction("🎓 生成学术图表", self)
        self.academic_visualization_act.setStatusTip("生成高质量学术论文图表")
        self.academic_visualization_act.triggered.connect(self.generate_academic_visualizations)
        self.tools_menu.addAction(self.academic_visualization_act)

        self.academic_report_act = QAction("📊 学术分析报告", self)
        self.academic_report_act.setStatusTip("生成完整的学术分析报告")
        self.academic_report_act.triggered.connect(self.generate_academic_report)
        self.tools_menu.addAction(self.academic_report_act)

        # 删除无用的视图菜单

        # 帮助菜单
        self.help_menu = self.menuBar().addMenu("帮助")
        self.help_menu.addAction(self.about_act)
        self.help_menu.addAction(self.help_act)

    def create_toolbars(self):
        """创建工具栏"""
        # 文件工具栏
        file_toolbar = self.addToolBar("文件")
        file_toolbar.addAction(self.new_project_act)
        file_toolbar.addAction(self.open_project_act)
        file_toolbar.addAction(self.save_project_act)

        # 分析工具栏 - 已集成到参数面板中，不再需要重复的工具栏按钮
        # analysis_toolbar = self.addToolBar("分析")
        # analysis_toolbar.addAction(self.parameter_sensitivity_act)
        # analysis_toolbar.addAction(self.generate_ai_parameters_act)
        # analysis_toolbar.addSeparator()
        # analysis_toolbar.addAction(self.quantum_optimization_act)

    def new_project(self):
        """创建新项目 - 开启新窗口"""
        try:
            # 创建新的主窗口实例
            new_window = MainWindow()
            new_window.show()
            
            # 设置窗口标题区分
            current_title = self.windowTitle()
            if "- 项目" not in current_title:
                self.setWindowTitle(f"{current_title} - 项目1")
            
            new_window.setWindowTitle(f"ReaxFFOpt - 先进的反应力场参数优化框架 - 新项目")
            
            self.status_bar.showMessage("已创建新项目窗口")
            
            print(" 新项目窗口已创建")
            
        except Exception as e:
            print(f" 创建新项目窗口失败: {e}")
            QMessageBox.critical(self, "错误", f"创建新项目失败：\n{str(e)}")
            
            # 如果新窗口创建失败，回退到清除当前数据的方式
            reply = QMessageBox.question(
                self, "新项目确认",
                "无法创建新窗口，是否清除当前数据创建新项目？",
                QMessageBox.Yes | QMessageBox.No, QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # 清除当前数据
                self.clear_current_project()
                self.status_bar.showMessage("已创建新项目（当前窗口）")
    
    def clear_current_project(self):
        """清除当前项目数据"""
        try:
            # 停止当前优化
            if hasattr(self, 'optimizer') and self.optimizer:
                self.stop_optimization()
            
            # 清除数据处理器
            if hasattr(self, 'dataset_handler'):
                self.dataset_handler = None
            
            # 清除参数表格
            if hasattr(self, 'parameter_panel'):
                self.parameter_panel.parameter_table.setRowCount(0)
            
            # 清除可视化
            if hasattr(self, 'visualization_panel'):
                # 清除图表
                for canvas_name, canvas in self.visualization_panel.canvases.items():
                    canvas.axes.clear()
                    canvas.draw()
            
            # 重置状态
            self.progress_label.setText("进度: 0%")
            
            print(" 当前项目数据已清除")
            
        except Exception as e:
            print(f" 清除项目数据时出错: {e}")

    def open_project(self):
        """打开项目"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "打开项目", "", "ReaxFFOpt项目文件 (*.roxp);;所有文件 (*.*)"
        )

        if file_path:
            self.status_bar.showMessage(f"已打开项目: {os.path.basename(file_path)}")

    def save_project(self):
        """保存项目"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存项目", "", "ReaxFFOpt项目文件 (*.roxp);;所有文件 (*.*)"
        )

        if file_path:
            if not file_path.endswith('.roxp'):
                file_path += '.roxp'
            self.status_bar.showMessage(f"已保存项目: {os.path.basename(file_path)}")

    def import_ffield(self):
        """导入力场文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "导入力场文件", "", "ReaxFF力场文件 (*.ffield);;所有文件 (*.*)"
        )

        if file_path:
            self.status_bar.showMessage(f"已导入力场文件: {os.path.basename(file_path)}")

    def import_structure(self):
        """导入分子结构"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "导入分子结构", "",
            "结构文件 (*.xyz *.pdb *.mol *.cif);;所有文件 (*.*)"
        )

        if file_path:
            self.status_bar.showMessage(f"已导入分子结构: {os.path.basename(file_path)}")

    def import_training_set(self):
        """导入训练集"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "导入训练集", "", "训练集文件 (*.geo);;所有文件 (*.*)"
        )

        if file_path:
            self.status_bar.showMessage(f"已导入训练集: {os.path.basename(file_path)}")

    def import_dataset_folder(self):
        """导入数据集文件夹 - 支持任意位置"""
        # 提供更好的起始目录
        start_dir = ""
        if hasattr(self, 'available_datasets') and self.available_datasets:
            start_dir = os.path.abspath("Datasets")
        
        folder_path = QFileDialog.getExistingDirectory(
            self, "选择数据集文件夹 (支持任意位置的数据集)", start_dir
        )

        if folder_path:
            self.import_dataset_from_path(folder_path)

    def import_dataset_from_path(self, folder_path):
        """从指定路径导入数据集"""
        print(f" 选择的数据集路径: {folder_path}")

        # 检测数据集类型
        dataset_info = self._detect_dataset_structure(folder_path)

        if dataset_info['type'] == 'single':
            # 单个数据集
            self._import_single_dataset(folder_path, dataset_info['name'])
        elif dataset_info['type'] == 'multiple':
            # 包含多个数据集的文件夹
            self._import_multiple_datasets(folder_path, dataset_info['datasets'])
        else:
            QMessageBox.warning(
                self, "无效的数据集",
                f"所选文件夹不包含有效的ReaxFF数据集。\n\n"
                f"有效数据集应包含以下文件之一：\n"
                f"• geo (几何结构文件)\n"
                f"• params (参数文件)\n"
                f"• trainset.in (训练集文件)\n"
                f"• ffield_* (力场文件)"
            )

    def import_dataset_folder_from_path(self, folder_path):
        """从参数面板调用的数据集导入方法"""
        print(f"📁 从参数面板导入数据集: {folder_path}")
        self.import_dataset_from_path(folder_path)

    def _scan_datasets(self, folder_path):
        """扫描数据集文件夹 - 优化版本，参考jaxreaxff_3实现"""
        datasets = []
        
        try:
            print(f" 开始扫描数据集文件夹: {folder_path}")
            
            if not os.path.exists(folder_path):
                print(f" 文件夹不存在: {folder_path}")
                return datasets
            
            # 获取所有子目录
            for item in os.listdir(folder_path):
                material_path = os.path.join(folder_path, item)
                if os.path.isdir(material_path):
                    # 检查是否包含有效的数据文件
                    has_valid_data = False
                    data_files_found = []
                    
                    # 检查常见的数据文件
                    data_files = ['geo', 'params', 'trainset.in', 'control', 'ffield']
                    for data_file in data_files:
                        if os.path.exists(os.path.join(material_path, data_file)):
                            has_valid_data = True
                            data_files_found.append(data_file)
                    
                    # 检查子目录
                    sub_datasets = []
                    for subitem in os.listdir(material_path):
                        sub_path = os.path.join(material_path, subitem)
                        if os.path.isdir(sub_path):
                            sub_has_data = False
                            for data_file in data_files:
                                if os.path.exists(os.path.join(sub_path, data_file)):
                                    sub_has_data = True
                                    data_files_found.append(f"{subitem}/{data_file}")
                                    break
                            
                            if sub_has_data:
                                sub_datasets.append(subitem)
                                has_valid_data = True
                    
                    if has_valid_data:
                        # 清理数据集名称
                        display_name = item
                        
                        # 特殊处理HNO3数据集
                        if item.lower() == "hno3":
                            display_name = "nitric_acid"
                        
                        # 如果有子数据集，添加到列表中
                        if sub_datasets:
                            for sub_dataset in sub_datasets:
                                if sub_dataset.lower() in ['valset', 'testset', 'trainset']:
                                    # 使用父级名称
                                    datasets.append(display_name)
                                else:
                                    # 使用子数据集名称
                                    datasets.append(sub_dataset)
                        else:
                            datasets.append(display_name)
                        
                        print(f" 发现有效数据集: {display_name}")
                        print(f"   数据文件: {', '.join(data_files_found)}")
                        if sub_datasets:
                            print(f"   子数据集: {', '.join(sub_datasets)}")
                    else:
                        print(f" 跳过无效数据集目录: {item} (无有效数据文件)")
            
            print(f" 总共发现 {len(datasets)} 个有效数据集: {datasets}")
            
        except Exception as e:
            print(f" 扫描数据集时出错: {e}")
            import traceback
            traceback.print_exc()
        
        return datasets

    def _detect_dataset_structure(self, folder_path):
        """检测数据集结构类型"""
        folder_name = os.path.basename(folder_path)
        
        # 检查是否是单个数据集
        direct_files = []
        try:
            direct_files = os.listdir(folder_path)
        except:
            return {'type': 'invalid'}
        
        # 检查ReaxFF关键文件
        files_lower = [f.lower() for f in direct_files if os.path.isfile(os.path.join(folder_path, f))]
        has_geo = any(f == 'geo' or f.endswith('.geo') for f in files_lower)
        has_params = any(f == 'params' for f in files_lower)
        has_ffield = any(f.startswith('ffield') for f in files_lower)
        has_trainset = any(f == 'trainset.in' for f in files_lower)
        
        if has_geo or has_params or has_ffield or has_trainset:
            # 这是一个单独的数据集
            return {
                'type': 'single',
                'name': folder_name,
                'files': {
                    'geo': has_geo,
                    'params': has_params,
                    'ffield': has_ffield,
                    'trainset': has_trainset
                }
            }
        
        # 检查是否包含多个数据集（子文件夹）
        subdirs = [d for d in direct_files if os.path.isdir(os.path.join(folder_path, d))]
        valid_datasets = []
        
        for subdir in subdirs:
            subdir_path = os.path.join(folder_path, subdir)
            subdir_files = []
            try:
                subdir_files = os.listdir(subdir_path)
            except:
                continue
                
            # 递归检查子目录中的文件
            all_files = []
            for root, dirs, files in os.walk(subdir_path):
                all_files.extend([f.lower() for f in files])
            
            sub_has_geo = any(f == 'geo' or f.endswith('.geo') for f in all_files)
            sub_has_params = any(f == 'params' for f in all_files)
            sub_has_ffield = any(f.startswith('ffield') for f in all_files)
            sub_has_trainset = any(f == 'trainset.in' for f in all_files)
            
            if sub_has_geo or sub_has_params or sub_has_ffield or sub_has_trainset:
                valid_datasets.append({
                    'name': subdir,
                    'path': subdir_path,
                    'files': {
                        'geo': sub_has_geo,
                        'params': sub_has_params,
                        'ffield': sub_has_ffield,
                        'trainset': sub_has_trainset
                    }
                })
        
        if valid_datasets:
            return {
                'type': 'multiple',
                'datasets': valid_datasets
            }
        
        return {'type': 'invalid'}

    def _import_single_dataset(self, folder_path, dataset_name):
        """导入单个数据集"""
        # 检查是否已存在该数据集
        existing_datasets = []
        if hasattr(self, 'dataset_handler') and self.dataset_handler:
            if hasattr(self.dataset_handler, 'parameters'):
                existing_datasets = list(self.dataset_handler.parameters.keys())

        if dataset_name in existing_datasets:
            # 数据集已存在，询问是否覆盖或添加
            reply = QMessageBox.question(
                self, "数据集已存在",
                f"数据集 '{dataset_name}' 已存在。\n\n"
                f"请选择操作：\n"
                f"• 是：添加为新数据集（保留现有数据）\n"
                f"• 否：覆盖现有数据集\n"
                f"• 取消：取消导入",
                QMessageBox.Yes | QMessageBox.No | QMessageBox.Cancel,
                QMessageBox.Yes
            )

            if reply == QMessageBox.Cancel:
                return
            elif reply == QMessageBox.No:
                # 覆盖模式 - 直接传递文件夹路径，不传递数据集名称列表
                self._start_dataset_import(folder_path, None, clear_data=True)
            else:
                # 添加模式 - 直接传递文件夹路径，不传递数据集名称列表
                self._start_dataset_import(folder_path, None, clear_data=False)
        else:
            # 新数据集，直接添加
            reply = QMessageBox.question(
                self, "确认导入数据集",
                f"确认导入数据集: {dataset_name}\n"
                f"位置: {folder_path}\n\n"
                f"这将添加新的数据集到现有数据中。",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )

            if reply == QMessageBox.Yes:
                # 直接传递文件夹路径，让DatasetWorker自动处理
                self._start_dataset_import(folder_path, None, clear_data=False)

    def _import_multiple_datasets(self, folder_path, datasets):
        """导入多个数据集 - 显示选择对话框"""
        dataset_names = [d['name'] for d in datasets]
        
        # 检查哪些数据集已存在
        existing_datasets = []
        if hasattr(self, 'dataset_handler') and self.dataset_handler:
            if hasattr(self.dataset_handler, 'parameters'):
                existing_datasets = list(self.dataset_handler.parameters.keys())
        
        existing_in_selection = [name for name in dataset_names if name in existing_datasets]
        
        dialog = DatasetSelectionDialog(dataset_names, self)
        dialog.setWindowTitle(f"选择要导入的数据集 - {os.path.basename(folder_path)}")
        
        # 显示每个数据集包含的文件信息
        info_text = "数据集信息:\n"
        for dataset in datasets:
            files_info = []
            for file_type, exists in dataset['files'].items():
                if exists:
                    files_info.append(file_type)
            
            # 标记已存在的数据集
            status_mark = " (已存在)" if dataset['name'] in existing_datasets else ""
            info_text += f"• {dataset['name']}{status_mark}: {', '.join(files_info)}\n"
        
        # 添加导入模式选择
        if existing_in_selection:
            info_text += f"\n注意：{len(existing_in_selection)} 个数据集已存在，将询问是否覆盖或添加。"
        
        # 添加信息标签到对话框
        info_label = QLabel(info_text)
        info_label.setWordWrap(True)
        dialog.layout().insertWidget(0, info_label)
        
        if dialog.exec_() == QDialog.Accepted:
            selected_datasets = dialog.get_selected_datasets()
            if selected_datasets:
                # 检查是否需要询问覆盖模式
                has_existing = any(name in existing_datasets for name in selected_datasets)
                
                if has_existing:
                    reply = QMessageBox.question(
                        self, "导入模式选择",
                        f"选中的数据集中有 {len(existing_in_selection)} 个已存在。\n\n"
                        f"请选择导入模式：\n"
                        f"• 是：添加模式（保留现有数据，新数据作为独立数据集）\n"
                        f"• 否：覆盖模式（替换已存在的数据集）",
                        QMessageBox.Yes | QMessageBox.No,
                        QMessageBox.Yes
                    )
                    clear_data = (reply == QMessageBox.No)
                else:
                    clear_data = False
                
                self._start_dataset_import(folder_path, selected_datasets, clear_data=clear_data)
            else:
                QMessageBox.warning(self, "警告", "请至少选择一个数据集")

    def _start_dataset_import(self, folder_path, selected_datasets, clear_data=False):
        """开始数据集导入过程"""
        mode_text = "覆盖" if clear_data else "添加"
        self.status_bar.showMessage(f"正在{mode_text}选中的数据集...")
        self.progress_label.setText("进度: 0%")

        # 处理数据集名称显示
        if selected_datasets:
            dataset_names = ', '.join(selected_datasets)
        else:
            dataset_names = os.path.basename(folder_path)

        print(f" 开始{mode_text}数据集: {dataset_names}")
        print(f" 源路径: {folder_path}")
        print(f" 模式: {'覆盖' if clear_data else '添加'}")

        # 创建数据处理线程
        self.data_worker = DataWorker(folder_path, selected_datasets, clear_data=clear_data)

        # 连接信号
        self.data_worker.progress.connect(self.update_dataset_progress)
        self.data_worker.finished.connect(self.dataset_import_finished)

        # 启动线程
        self.data_worker.start()

    def check_default_datasets(self):
        """检查是否存在默认数据集（仅显示信息，不自动导入）"""
        default_datasets_path = "Datasets"
        self.available_datasets = []
        
        if os.path.exists(default_datasets_path) and os.path.isdir(default_datasets_path):
            # 扫描可用的数据集
            for item in os.listdir(default_datasets_path):
                item_path = os.path.join(default_datasets_path, item)
                if os.path.isdir(item_path):
                    self.available_datasets.append({
                        'name': item,
                        'path': item_path
                    })
            
            if self.available_datasets:
                self.status_bar.showMessage(f"发现 {len(self.available_datasets)} 个可用数据集，可通过菜单导入")
                print(f" 发现可用数据集: {[d['name'] for d in self.available_datasets]}")
                
                # 检查参数表格是否为空，如果为空则提示用户导入
                if hasattr(self, 'parameter_panel') and self.parameter_panel:
                    if self.parameter_panel.parameter_table.rowCount() == 0:
                        reply = QMessageBox.question(
                            self, "发现数据集", 
                            f"检测到 {len(self.available_datasets)} 个数据集，但参数表格为空。\n\n"
                            f"是否要自动导入这些数据集？\n\n"
                            f"• 是：自动导入所有数据集\n"
                            f"• 否：稍后手动导入",
                            QMessageBox.Yes | QMessageBox.No,
                            QMessageBox.Yes
                        )
                        
                        if reply == QMessageBox.Yes:
                            print(" 用户选择自动导入数据集")
                            self.quick_import_default_datasets()
            else:
                self.status_bar.showMessage("未发现可用数据集，请导入数据集文件夹")
        else:
            self.status_bar.showMessage("Datasets目录不存在，请导入数据集文件夹")
            print(" 提示: 可以通过 '文件' > '导入' > '导入数据集文件夹' 来导入数据")
        
        #  移除：不再自动加载任何模拟数据
        # 只显示提示信息，等待用户主动导入

    def quick_import_default_datasets(self):
        """快速导入默认数据集文件夹"""
        default_datasets_path = "Datasets"
        
        if not os.path.exists(default_datasets_path):
            QMessageBox.information(
                self, "未找到默认数据集", 
                f"默认数据集文件夹不存在: {default_datasets_path}\n\n"
                "您可以：\n"
                "1. 使用 '导入数据集文件夹' 选择任意位置的数据集\n"
                "2. 创建Datasets文件夹并放入您的数据集"
            )
            return
        
        # 使用新的导入系统
        self.import_dataset_from_path(default_datasets_path)

    def update_dataset_progress(self, value):
        """更新数据集导入进度"""
        self.progress_label.setText(f"进度: {value}%")

    def dataset_import_finished(self, result):
        """数据集导入完成处理 - 加强调试和错误处理"""
        print(f" 数据集导入完成回调被触发")
        print(f" 导入结果: {result}")
        
        # 显示导入结果
        message = f"数据集导入完成：\n"
        message += f"- 结构文件: {result.get('structures', 0)}个\n"
        message += f"- 能量数据: {result.get('energies', 0)}个\n"
        message += f"- 力数据: {result.get('forces', 0)}个\n"
        message += f"- 训练集: {result.get('training_sets', 0)}个"
        if 'parameters' in result:
            message += f"\n- 参数: {result['parameters']}个"

        QMessageBox.information(self, "数据集导入", message)

        # 更新界面上的相关组件
        self.status_bar.showMessage(f"数据集导入完成")

        # 检查数据处理器是否可用
        if not hasattr(self, 'data_worker'):
            print(" 错误: data_worker 不存在")
            QMessageBox.warning(self, "数据处理错误", "数据处理器不可用，请重新导入数据集")
            return
        
        if not self.data_worker:
            print(" 错误: data_worker 为 None")
            QMessageBox.warning(self, "数据处理错误", "数据处理器为空，请重新导入数据集")
            return
        
        if not hasattr(self.data_worker, 'handler'):
            print(" 错误: data_worker.handler 不存在")
            QMessageBox.warning(self, "数据处理错误", "数据处理句柄不存在，请重新导入数据集")
            return
        
        if not self.data_worker.handler:
            print(" 错误: data_worker.handler 为 None")
            QMessageBox.warning(self, "数据处理错误", "数据处理句柄为空，请重新导入数据集")
            return

        # 设置或更新数据集处理器引用
        if not hasattr(self, 'dataset_handler') or not self.dataset_handler:
            # 第一次导入，直接设置
            self.dataset_handler = self.data_worker.handler
            print(f" 首次设置数据集处理器引用: {type(self.dataset_handler)}")
        else:
            # 已有数据集处理器，合并新数据
            new_handler = self.data_worker.handler
            print(f" 合并新数据到现有数据集处理器")

            # 合并参数
            if hasattr(new_handler, 'parameters'):
                for dataset_name, params in new_handler.parameters.items():
                    self.dataset_handler.parameters[dataset_name] = params
                    print(f"   合并参数: {dataset_name} ({len(params)} 个参数)")

            # 合并结构
            if hasattr(new_handler, 'structures'):
                for dataset_name, structures in new_handler.structures.items():
                    self.dataset_handler.structures[dataset_name] = structures
                    print(f"   合并结构: {dataset_name} ({len(structures)} 个结构)")

            # 合并训练集
            if hasattr(new_handler, 'training_sets'):
                for dataset_name, training_set in new_handler.training_sets.items():
                    self.dataset_handler.training_sets[dataset_name] = training_set
                    print(f"   合并训练集: {dataset_name}")

            # 合并数据集路径信息
            if hasattr(new_handler, 'dataset_paths'):
                for dataset_name, path in new_handler.dataset_paths.items():
                    self.dataset_handler.dataset_paths[dataset_name] = path
                    print(f"   合并路径: {dataset_name} -> {path}")

        print(f" 数据集处理器包含 {len(self.dataset_handler.parameters) if hasattr(self.dataset_handler, 'parameters') else 0} 个数据集")

        # 如果有参数面板，更新它
        if hasattr(self, 'parameter_panel') and self.parameter_panel:
            try:
                print(" 开始更新参数面板...")
                
                # 更新训练集文件路径
                if hasattr(self.parameter_panel, 'config_panel') and hasattr(self.parameter_panel.config_panel, 'training_file_edit'):
                    training_path = os.path.join(self.data_worker.folder_path, "trainset.in")
                    self.parameter_panel.config_panel.training_file_edit.setText(training_path)
                    print(f" 已更新训练集路径: {training_path}")

                # 获取所有参数（从合并后的数据集处理器）
                print(" 正在获取参数数据...")
                parameters = self.dataset_handler.get_all_parameters()
                print(f" 获取到的参数数据集: {list(parameters.keys()) if parameters else '无'}")
                print(f" 参数详情: {parameters}")

                if parameters and len(parameters) > 0:
                    # 清空现有参数表格，重新构建以包含所有数据集
                    print(" 清空现有参数表格...")
                    print(" 开始按数据集分类更新参数表格...")
                    self.parameter_panel.parameter_table.setRowCount(0)
                    row_count = 0

                    # 构建层级结构
                    print(" 构建数据集层级结构...")
                    dataset_hierarchy = self._build_dataset_hierarchy(parameters.keys())

                    # 按层级显示参数
                    total_datasets = len(parameters)
                    print(f" 总数据集数量: {total_datasets}")
                    
                    # 如果数据集太多，提供简化显示选项
                    if total_datasets > 20:
                        reply = QMessageBox.question(
                            self, "大量数据集检测", 
                            f"检测到 {total_datasets} 个数据集，这可能会影响界面性能。\n\n"
                            f"建议操作：\n"
                            f"• 是：仅显示数据集概要（推荐）\n"
                            f"• 否：显示所有参数详情（可能较慢）\n",
                            QMessageBox.Yes | QMessageBox.No,
                            QMessageBox.Yes
                        )
                        simplified_mode = (reply == QMessageBox.Yes)
                    else:
                        simplified_mode = False
                    
                    print(f" 使用{'简化' if simplified_mode else '详细'}显示模式")
                    
                    if simplified_mode:
                        # 简化模式：只显示数据集概要
                        row_count = self._add_datasets_summary_mode(parameters, row_count)
                    else:
                        # 详细模式：显示所有参数
                        for parent_dataset, children in dataset_hierarchy.items():
                            if children:
                                # 有子数据集的父级
                                row_count = self._add_parent_dataset_row(parent_dataset, children, parameters, row_count)
                                # 添加子数据集
                                for child_dataset in children:
                                    if child_dataset in parameters:
                                        row_count = self._add_child_dataset_row(child_dataset, parameters[child_dataset], row_count)
                            else:
                                # 独立数据集
                                if parent_dataset in parameters:
                                    row_count = self._add_dataset_row(parent_dataset, parameters[parent_dataset], row_count, 0)

                    print(f" 已更新参数表格：{len(parameters)} 个数据集，共 {sum(len(params) for params in parameters.values())} 个参数")
                    
                    # 🔧 保持预设的列宽，不自动调整
                    # self.parameter_panel.parameter_table.resizeColumnsToContents()  # 注释掉，保持预设列宽
                    print("✅ 保持预设列宽，不进行自动调整")
                    
                    # 显示成功消息
                    QMessageBox.information(
                        self, "参数导入成功", 
                        f"成功导入 {len(parameters)} 个数据集的参数！\n"
                        f"总参数数量: {sum(len(params) for params in parameters.values())}\n\n"
                        f"您现在可以：\n"
                        f"• 选择要优化的参数\n"
                        f"• 使用折叠功能整理界面\n"
                        f"• 开始参数优化"
                    )
                    
                else:
                    print(" 未找到参数数据")
                    QMessageBox.warning(
                        self, "参数导入失败", 
                        "未能从数据集中提取参数信息。\n\n"
                        f"请确认数据集文件夹包含有效的params文件。\n"
                        f"当前处理的数据集: {list(parameters.keys()) if parameters else '无'}"
                    )
                    
            except Exception as e:
                print(f" 更新参数面板时出错: {str(e)}")
                import traceback
                traceback.print_exc()
                QMessageBox.critical(
                    self, "参数更新错误", 
                    f"更新参数面板时发生错误：\n{str(e)}\n\n"
                    f"请尝试重新导入数据集。"
                )
        else:
            print(" 错误: parameter_panel 不存在或为None")

        # 如果有可视化面板，更新它
        if hasattr(self, 'visualization_panel'):
            try:
                structures = self.data_worker.handler.get_all_structures()
                if structures:
                    self.visualization_panel.update_structures(structures)
                    print(" 已更新可视化面板结构")
                    
                    # 同时更新反应路径面板的结构
                    if hasattr(self, 'reaction_path_panel'):
                        self.reaction_path_panel.update_structures(structures)
                        print(" 已更新反应路径面板结构")
                
                # 更新可视化面板的数据集选择器
                if hasattr(self.visualization_panel, 'multi_dataset_history'):
                    # 清空多数据集历史
                    self.visualization_panel.multi_dataset_history.clear()
                    
                    # 初始化当前数据集列表
                    if hasattr(self.visualization_panel, 'current_datasets'):
                        self.visualization_panel.current_datasets = []
                    
                    # 获取所有数据集名称并添加到可视化面板
                    if hasattr(self.data_worker.handler, 'parameters') and self.data_worker.handler.parameters:
                        dataset_names = list(self.data_worker.handler.parameters.keys())
                        print(f" 更新可视化面板数据集选择器: {len(dataset_names)} 个数据集")
                        
                        # 为每个数据集初始化历史记录
                        for dataset_name in dataset_names:
                            self.visualization_panel.multi_dataset_history[dataset_name] = {
                                'iterations': [],
                                'total_loss': [],
                                'train_loss': [],
                                'val_loss': [],
                                'parameters': []
                            }
                            if dataset_name not in self.visualization_panel.current_datasets:
                                self.visualization_panel.current_datasets.append(dataset_name)
                        
                        # 更新数据集选择器
                        if hasattr(self.visualization_panel, 'update_dataset_selector'):
                            self.visualization_panel.update_dataset_selector()
                            print(f" 已更新数据集选择器，包含 {len(dataset_names)} 个数据集")
                        
                        # 更新数据集信息标签
                        if hasattr(self.visualization_panel, 'update_dataset_info_label'):
                            self.visualization_panel.update_dataset_info_label()
                            print(" 已更新数据集信息标签")
                        
                        # 为导入的数据集生成演示数据（如果没有的话）
                        if hasattr(self.visualization_panel, 'load_demo_data'):
                            self.visualization_panel.load_demo_data()
                            print(" 已为导入的数据集生成演示数据")
                
            except Exception as e:
                print(f" 更新可视化面板时出错: {e}")
                import traceback
                traceback.print_exc()

    def _add_datasets_summary_mode(self, parameters, row_count):
        """简化模式：只显示数据集概要"""
        total_params = sum(len(params) for params in parameters.values())
        
        # 添加总体概要行
        self.parameter_panel.parameter_table.insertRow(row_count)
        
        summary_title = QTableWidgetItem(f" 数据集总览 ({len(parameters)} 个数据集)")
        summary_title.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        summary_title.setBackground(QColor(100, 150, 200))
        summary_title.setFlags(Qt.ItemIsEnabled)
        self.parameter_panel.parameter_table.setItem(row_count, 0, summary_title)
        
        total_item = QTableWidgetItem(f"总计 {total_params} 个参数")
        total_item.setBackground(QColor(100, 150, 200))
        total_item.setFlags(Qt.ItemIsEnabled)
        total_item.setTextAlignment(Qt.AlignCenter)
        self.parameter_panel.parameter_table.setItem(row_count, 1, total_item)
        
        # 其他列填充
        for col in range(2, 5):
            empty_item = QTableWidgetItem("")
            empty_item.setBackground(QColor(100, 150, 200))
            empty_item.setFlags(Qt.ItemIsEnabled)
            self.parameter_panel.parameter_table.setItem(row_count, col, empty_item)
        
        row_count += 1
        
        # 按数据集分别显示概要
        for dataset_name, dataset_params in parameters.items():
            self.parameter_panel.parameter_table.insertRow(row_count)
            
            # 第0列：添加数据集复选框
            checkbox_item = QTableWidgetItem()
            checkbox_item.setFlags(Qt.ItemIsUserCheckable | Qt.ItemIsEnabled)
            checkbox_item.setCheckState(Qt.Unchecked)
            checkbox_item.setBackground(QColor(230, 240, 250))
            self.parameter_panel.parameter_table.setItem(row_count, 0, checkbox_item)

            # 第1列：数据集名称（可展开查看详情）
            dataset_item = QTableWidgetItem(f"   {dataset_name}")
            dataset_item.setFont(QFont("Microsoft YaHei", 10))
            dataset_item.setBackground(QColor(230, 240, 250))
            dataset_item.setFlags(Qt.ItemIsEnabled)
            dataset_item.setData(Qt.UserRole, {'type': 'dataset_summary', 'name': dataset_name})
            self.parameter_panel.parameter_table.setItem(row_count, 1, dataset_item)
            
            # 参数数量
            count_item = QTableWidgetItem(f"{len(dataset_params)} 个参数")
            count_item.setBackground(QColor(230, 240, 250))
            count_item.setFlags(Qt.ItemIsEnabled)
            count_item.setTextAlignment(Qt.AlignCenter)
            self.parameter_panel.parameter_table.setItem(row_count, 1, count_item)
            
            # 参数范围统计 - 修复：处理参数可能是浮点数的情况
            values = []
            for p in dataset_params.values():
                if isinstance(p, dict) and 'value' in p:
                    values.append(p['value'])
                elif isinstance(p, (int, float)):
                    values.append(float(p))
                else:
                    # 跳过无法处理的参数
                    continue
                    
            if values:
                range_info = f"{min(values):.2f} ~ {max(values):.2f}"
            else:
                range_info = "无数据"
            range_item = QTableWidgetItem(range_info)
            range_item.setBackground(QColor(230, 240, 250))
            range_item.setFlags(Qt.ItemIsEnabled)
            range_item.setTextAlignment(Qt.AlignCenter)
            self.parameter_panel.parameter_table.setItem(row_count, 2, range_item)
            
            # 展开按钮 - 修复折叠功能
            button_widget = QWidget()
            button_layout = QHBoxLayout(button_widget)
            button_layout.setContentsMargins(2, 2, 2, 2)
            
            expand_button = QPushButton("展开详情")
            expand_button.setMaximumSize(80, 25)
            expand_button.setStyleSheet("""
                QPushButton {
                    background-color: #2196F3;
                    color: white;
                    border: none;
                    border-radius: 3px;
                    font-size: 10px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #1976D2;
                }
            """)
            # 修复展开按钮连接
            def create_expand_handler(dataset_name):
                return lambda: self._expand_dataset_details(dataset_name)
            
            expand_button.clicked.connect(create_expand_handler(dataset_name))
            
            button_layout.addWidget(expand_button)
            button_layout.addStretch()
            
            self.parameter_panel.parameter_table.setCellWidget(row_count, 3, button_widget)
            
            # 敏感性占位
            sens_item = QTableWidgetItem("摘要模式")
            sens_item.setBackground(QColor(230, 240, 250))
            sens_item.setFlags(Qt.ItemIsEnabled)
            sens_item.setTextAlignment(Qt.AlignCenter)
            self.parameter_panel.parameter_table.setItem(row_count, 4, sens_item)
            
            row_count += 1
        
        return row_count
    
    def _expand_dataset_details(self, dataset_name):
        """展开数据集详情 - 实际展开参数显示"""
        try:
            # 询问用户是否要展开显示详细参数
            reply = QMessageBox.question(
                self, f"展开数据集 - {dataset_name}",
                f"是否要展开显示数据集 '{dataset_name}' 的详细参数？\n\n"
                f"注意：这会将简化视图切换为详细视图。",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )
            
            if reply == QMessageBox.Yes:
                # 重新以详细模式加载参数
                if hasattr(self, 'dataset_handler') and self.dataset_handler:
                    print(f" 重新以详细模式加载数据集: {dataset_name}")
                    
                    # 获取所有参数
                    parameters = self.dataset_handler.get_all_parameters()
                    
                    if parameters and dataset_name in parameters:
                        # 清空当前表格
                        self.parameter_panel.parameter_table.setRowCount(0)
                        
                        # 以详细模式重新添加这个数据集
                        row_count = 0
                        dataset_params = parameters[dataset_name]
                        row_count = self._add_dataset_row(dataset_name, dataset_params, row_count, 0)
                        
                        # 🔧 保持预设的列宽，不自动调整
                        # self.parameter_panel.parameter_table.resizeColumnsToContents()  # 注释掉，保持预设列宽
                        print("✅ 展开数据集时保持预设列宽")
                        
                        QMessageBox.information(
                            self, "展开完成",
                            f"数据集 '{dataset_name}' 已展开显示！\n"
                            f"显示了 {len(dataset_params)} 个参数的详细信息。"
                        )
                        
                    else:
                        QMessageBox.warning(
                            self, "数据未找到",
                            f"无法找到数据集 '{dataset_name}' 的参数数据。"
                        )
                else:
                    QMessageBox.warning(
                        self, "数据集未加载",
                        "请先导入数据集。"
                    )
        except Exception as e:
            print(f" 展开数据集详情时出错: {e}")
            QMessageBox.critical(
                self, "展开失败",
                f"展开数据集详情时发生错误：\n{str(e)}"
            )

    def _build_dataset_hierarchy(self, dataset_names):
        """构建数据集层级结构 - 基于路径的父子关系判断"""
        hierarchy = {}
        dataset_list = list(dataset_names)
        
        print(f"🔍 构建层级结构，输入数据集: {dataset_list}")
        
        # 获取数据集路径信息
        dataset_paths = {}
        try:
            if hasattr(self, 'data_worker') and hasattr(self.data_worker, 'handler'):
                dataset_paths = self.data_worker.handler.dataset_paths
                print(f" 数据集路径信息: {dataset_paths}")
        except:
            print(" 未找到数据集路径信息，使用名称匹配")
        
        # 找出所有父-子关系
        parent_child_pairs = []
        
        # 主要方法：基于路径的父子关系判断
        if dataset_paths:
            for dataset in dataset_list:
                for potential_parent in dataset_list:
                    if potential_parent != dataset:
                        # 检查是否是路径包含关系
                        if '/' in dataset and dataset.startswith(potential_parent + '/'):
                            # 确保是直接父子关系，不是更深层的关系
                            relative_path = dataset[len(potential_parent) + 1:]
                            if '/' not in relative_path:  # 确保是直接子目录
                                parent_child_pairs.append((potential_parent, dataset))
                                print(f"   找到路径父子关系: {potential_parent} -> {dataset}")
        
        # 备用方法：基于名称模式的父子关系判断
        for dataset in dataset_list:
            for potential_parent in dataset_list:
                if potential_parent != dataset:
                    # 检查是否是名称包含关系（如 disulfide/valSet）
                    if '/' in dataset:
                        parts = dataset.split('/')
                        if len(parts) >= 2 and parts[0] == potential_parent:
                            if (potential_parent, dataset) not in parent_child_pairs:
                                parent_child_pairs.append((potential_parent, dataset))
                                print(f"   找到名称父子关系: {potential_parent} -> {dataset}")
        
        # 备用方法1: 数字_父名称 (如 1_NO2, 2_NO2 -> NO2)
        for dataset in dataset_list:
            if '_' in dataset:
                parts = dataset.split('_')
                if len(parts) >= 2:
                    first_part = parts[0]
                    if first_part.isdigit():
                        potential_parent = '_'.join(parts[1:])
                        if potential_parent in dataset_list:
                            if (potential_parent, dataset) not in parent_child_pairs:
                                parent_child_pairs.append((potential_parent, dataset))
                                print(f"   找到名称父子关系: {potential_parent} -> {dataset} (数字_父名称)")
        
        # 备用方法2: 父名称_数字 (如 HNO2_1, HNO2_2 -> HNO2)
        for dataset in dataset_list:
            if '_' in dataset:
                parts = dataset.split('_')
                if len(parts) >= 2:
                    last_part = parts[-1]
                    if last_part.isdigit():
                        potential_parent = '_'.join(parts[:-1])
                        if potential_parent in dataset_list:
                            if (potential_parent, dataset) not in parent_child_pairs:
                                parent_child_pairs.append((potential_parent, dataset))
                                print(f"   找到名称父子关系: {potential_parent} -> {dataset} (父名称_数字)")
        
        # 备用方法3: 相似名称检测 (如 HNO2 -> HNO2_test, HNO2_v2 等)
        for dataset in dataset_list:
            for potential_parent in dataset_list:
                if (potential_parent != dataset and 
                    dataset.startswith(potential_parent) and 
                    len(dataset) > len(potential_parent)):
                    
                    suffix = dataset[len(potential_parent):]
                    # 检查是否是常见的子数据集后缀
                    if suffix.startswith('_') or suffix.startswith('-') or suffix.startswith('/'):
                        suffix_part = suffix[1:]  # 去掉分隔符
                        # 数字、版本标识、测试标识等
                        if (suffix_part.isdigit() or 
                            suffix_part.lower() in ['test', 'v2', 'new', 'backup', 'old', 'valset'] or
                            suffix_part.startswith('v') and suffix_part[1:].isdigit()):
                            
                            # 避免重复添加
                            if (potential_parent, dataset) not in parent_child_pairs:
                                parent_child_pairs.append((potential_parent, dataset))
                                print(f"   找到相似名称父子关系: {potential_parent} -> {dataset}")
        
        # 构建层级字典
        for parent, child in parent_child_pairs:
            if parent not in hierarchy:
                hierarchy[parent] = []
            if child not in hierarchy[parent]:
                hierarchy[parent].append(child)
        
        # 添加没有子集的独立数据集
        for dataset in dataset_list:
            is_child = any(dataset in children for children in hierarchy.values())
            if not is_child and dataset not in hierarchy:
                hierarchy[dataset] = []
        
        print(f" 构建的层级结构: {hierarchy}")
        
        # 验证并显示结果
        for parent, children in hierarchy.items():
            if children:
                print(f"   {parent} 包含 {len(children)} 个子集: {children}")
            else:
                print(f"    {parent} 是独立数据集")
        
        return hierarchy
    
    def _add_parent_dataset_row(self, parent_name, children, all_parameters, row_count):
        """添加父数据集行（可折叠） - 确保折叠功能完整"""
        self.parameter_panel.parameter_table.insertRow(row_count)
        
        # 计算总参数数
        total_params = 0
        if parent_name in all_parameters:
            total_params += len(all_parameters[parent_name])
        for child in children:
            if child in all_parameters:
                total_params += len(all_parameters[child])
        
        # 第0列：添加父数据集复选框 - 增大点击区域
        checkbox_item = QTableWidgetItem()
        checkbox_item.setFlags(Qt.ItemIsUserCheckable | Qt.ItemIsEnabled | Qt.ItemIsSelectable)
        checkbox_item.setCheckState(Qt.Unchecked)
        checkbox_item.setBackground(QColor(180, 200, 240))
        # 🔧 设置复选框文本，增大点击区域
        checkbox_item.setText("☐")  # 使用Unicode复选框符号
        checkbox_item.setTextAlignment(Qt.AlignCenter)
        self.parameter_panel.parameter_table.setItem(row_count, 0, checkbox_item)

        # 第1列：创建可折叠的父级标题
        parent_title = QTableWidgetItem(f" {parent_name} ({len(children)} 个子集, {total_params} 个参数)")
        parent_title.setFont(QFont("Microsoft YaHei", 10, QFont.Bold))
        parent_title.setBackground(QColor(180, 200, 240))  # 更深的蓝色
        parent_title.setFlags(Qt.ItemIsEnabled)
        parent_title.setData(Qt.UserRole, {'type': 'parent', 'name': parent_name, 'expanded': True})
        self.parameter_panel.parameter_table.setItem(row_count, 1, parent_title)
        
        # 创建折叠按钮容器
        button_widget = QWidget()
        button_layout = QHBoxLayout(button_widget)
        button_layout.setContentsMargins(2, 2, 2, 2)
        
        # 添加折叠按钮
        collapse_button = QPushButton("▼ 折叠")
        collapse_button.setMaximumSize(60, 25)
        collapse_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 3px;
                font-size: 10px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3e8e41;
            }
        """)
        
        # 使用更稳定的连接方式，确保参数传递正确
        def create_toggle_handler(dataset_name):
            return lambda: self._toggle_dataset_collapse(dataset_name)
        
        collapse_button.clicked.connect(create_toggle_handler(parent_name))
        
        button_layout.addWidget(collapse_button)
        button_layout.addStretch()  # 添加弹性空间
        
        self.parameter_panel.parameter_table.setCellWidget(row_count, 1, button_widget)
        
        # 统计信息
        stats_item = QTableWidgetItem(f"共 {total_params} 个参数")
        stats_item.setBackground(QColor(180, 200, 240))
        stats_item.setFlags(Qt.ItemIsEnabled)
        stats_item.setTextAlignment(Qt.AlignCenter)
        self.parameter_panel.parameter_table.setItem(row_count, 2, stats_item)
        
        # 其他列填充
        for col in range(3, 5):
            empty_item = QTableWidgetItem("")
            empty_item.setBackground(QColor(180, 200, 240))
            empty_item.setFlags(Qt.ItemIsEnabled)
            self.parameter_panel.parameter_table.setItem(row_count, col, empty_item)
        
        print(f"    添加父数据集: {parent_name} ({len(children)} 个子集, 已添加折叠按钮)")
        return row_count + 1

    def _toggle_dataset_collapse(self, dataset_name):
        """切换数据集折叠状态 - 支持所有类型数据集"""
        table = self.parameter_panel.parameter_table
        
        print(f" 切换折叠状态: {dataset_name}")
        
        # 查找数据集行（支持parent、dataset、child类型）
        dataset_row = -1
        dataset_type = None

        for row in range(table.rowCount()):
            try:
                # 先检查第0列（复选框列）
                item = table.item(row, 0)
                if item and item.data(Qt.UserRole):
                    data = item.data(Qt.UserRole)
                    if (isinstance(data, dict) and
                        data.get('name') == dataset_name and
                        data.get('type') in ['parent', 'dataset', 'child']):
                        dataset_row = row
                        dataset_type = data.get('type')
                        break

                # 如果第0列没有，检查第1列（名称列）
                item = table.item(row, 1)
                if item and item.data(Qt.UserRole):
                    data = item.data(Qt.UserRole)
                    if (isinstance(data, dict) and
                        data.get('name') == dataset_name and
                        data.get('type') in ['parent', 'dataset', 'child']):
                        dataset_row = row
                        dataset_type = data.get('type')
                        break
            except Exception as e:
                print(f"    检查第{row}行时出错: {e}")
                continue
        
        if dataset_row == -1:
            print(f"    未找到数据集行: {dataset_name}")
            return
        
        # 获取当前状态（优先从第0列获取，如果没有则从第1列获取）
        dataset_item = table.item(dataset_row, 0)
        data = dataset_item.data(Qt.UserRole) if dataset_item else None
        if not data:
            dataset_item = table.item(dataset_row, 1)
            data = dataset_item.data(Qt.UserRole) if dataset_item else {}

        is_expanded = data.get('expanded', False)  # 默认为折叠状态
        
        print(f"   数据集类型: {dataset_type}, 当前展开状态: {is_expanded}")
        
        # 更新折叠按钮和状态
        button_widget = table.cellWidget(dataset_row, 1)
        if button_widget:
            # 查找按钮
            collapse_button = button_widget.findChild(QPushButton)
            if collapse_button:
                if is_expanded:
                    # 当前是展开状态，点击后要折叠
                    collapse_button.setText("▶")
                    collapse_button.setStyleSheet("""
                        QPushButton {
                            background-color: #ff9800;
                            color: white;
                            border: none;
                            border-radius: 10px;
                            font-size: 10px;
                            font-weight: bold;
                        }
                        QPushButton:hover {
                            background-color: #e68900;
                        }
                    """)
                    data['expanded'] = False
                    print(f"   折叠 {dataset_name}")
                else:
                    # 当前是折叠状态，点击后要展开
                    collapse_button.setText("▼")
                    collapse_button.setStyleSheet("""
                        QPushButton {
                            background-color: #4CAF50;
                            color: white;
                            border: none;
                            border-radius: 10px;
                            font-size: 10px;
                            font-weight: bold;
                        }
                        QPushButton:hover {
                            background-color: #45a049;
                        }
                    """)
                    data['expanded'] = True
                    print(f"   展开 {dataset_name}")

                # 更新数据到两个位置
                if table.item(dataset_row, 0):
                    table.item(dataset_row, 0).setData(Qt.UserRole, data)
                if table.item(dataset_row, 1):
                    table.item(dataset_row, 1).setData(Qt.UserRole, data)
        else:
            print(f"    未找到折叠按钮控件")
            # 强制刷新界面
            table.viewport().update()
            return
        
        # 显示/隐藏相关的子行 - 根据数据集类型处理
        rows_affected = 0
        current_row = dataset_row + 1
        target_hidden = not is_expanded  # 如果当前是展开状态，则要隐藏子行
        
        print(f"   目标隐藏状态: {target_hidden}")
        
        # 不同类型数据集的子项匹配规则
        if dataset_type == 'parent':
            # 父数据集：隐藏子数据集和参数
            child_types = ['child', 'parameter']
            child_prefixes = ['  ├─', '    └─', '      └─']
        elif dataset_type == 'dataset':
            # 独立数据集：只隐藏直接参数
            child_types = ['parameter']
            child_prefixes = ['  └─', '    └─']
        elif dataset_type == 'child':
            # 子数据集：隐藏子参数
            child_types = ['parameter']
            child_prefixes = ['    └─', '      └─']
        else:
            child_types = []
            child_prefixes = []
        
        while current_row < table.rowCount():
            try:
                # 检查第1列（名称列）的数据
                name_item = table.item(current_row, 1)
                if not name_item:
                    current_row += 1
                    continue

                text = name_item.text() or ""
                item_data = name_item.data(Qt.UserRole)

                # 通过缩进判断是否是子项
                is_child_by_prefix = any(text.startswith(prefix) for prefix in child_prefixes)

                # 通过UserRole数据判断
                is_child_by_data = False
                if item_data and isinstance(item_data, dict):
                    item_type = item_data.get('type', 'unknown')
                    item_dataset = item_data.get('dataset', '')

                    # 如果遇到新的数据集，停止
                    if item_type in ['parent', 'dataset', 'child'] and item_type != 'parameter':
                        # 检查是否属于当前数据集
                        item_name = item_data.get('name', '')
                        if item_name != dataset_name:
                            break

                    # 如果是参数类型且属于当前数据集
                    if item_type == 'parameter' and item_dataset == dataset_name:
                        is_child_by_data = True

                # 如果是子项，则隐藏/显示
                if is_child_by_prefix or is_child_by_data:
                    table.setRowHidden(current_row, target_hidden)
                    rows_affected += 1
                    print(f"     {'隐藏' if target_hidden else '显示'}第{current_row}行: {text[:30]}...")

                current_row += 1

            except Exception as e:
                print(f"    处理第{current_row}行时出错: {e}")
                current_row += 1
                continue
        
        print(f"   影响了 {rows_affected} 行")
        
        # 强制刷新表格显示
        table.viewport().update()
        table.update()
        # 立即处理界面更新
        try:
            from PyQt5.QtWidgets import QApplication
            QApplication.processEvents()
        except:
            pass

    @pyqtSlot(dict)
    def start_optimization(self, config):
        """开始优化过程"""
        self.status_bar.showMessage(f"正在运行优化: {config['method']}")
        self.stop_opt_act.setEnabled(True)

        # 重置搜索路径历史  重要：每次新优化都重新开始
        if hasattr(self, 'search_path_history'):
            del self.search_path_history
        if hasattr(self, 'loss_history'):
            del self.loss_history
        if hasattr(self, 'train_loss_history'):
            del self.train_loss_history
        if hasattr(self, 'val_loss_history'):
            del self.val_loss_history
        print(" 已重置搜索路径历史，准备记录新的优化轨迹")

        # 重置可视化面板的历史数据  新增：清除可视化历史
        if hasattr(self.visualization_panel, 'iterations_list'):
            del self.visualization_panel.iterations_list
        if hasattr(self.visualization_panel, 'total_losses'):
            del self.visualization_panel.total_losses
        if hasattr(self.visualization_panel, 'train_losses'):
            del self.visualization_panel.train_losses
        if hasattr(self.visualization_panel, 'val_losses'):
            del self.visualization_panel.val_losses
        print(" 已重置可视化面板历史数据")

        # 获取选中的参数
        parameters = config['parameters']
        if not parameters:
            QMessageBox.warning(self, "参数错误", "没有选择要优化的参数")
            return

        # 检查是否已导入数据集
        if not hasattr(self, 'dataset_handler') or self.dataset_handler is None:
            print(f" 优化开始时的数据集检查:")
            print(f"   - dataset_handler 存在: {hasattr(self, 'dataset_handler')}")
            print(f"   - dataset_handler 值: {self.dataset_handler if hasattr(self, 'dataset_handler') else 'None'}")
            
            QMessageBox.warning(
                self, "数据集未导入", 
                "请先导入数据集再开始优化。\n\n"
                "您可以通过菜单 '文件' > '导入' 来导入数据集。"
            )
            return

        # 创建优化器
        try:
            from optimizer.qt_optimizer_wrapper import create_qt_optimizer
            from optimizer.objectives import create_objective_function

            #  获取左侧参数面板选中的数据集
            selected_datasets = []
            if hasattr(self, 'parameter_panel') and hasattr(self.parameter_panel, 'parameter_table'):
                # 从参数表格获取选中的数据集
                selected_datasets = list(self.parameter_panel.parameter_table.selected_datasets)

            print(f"🎯 左侧选中的数据集: {selected_datasets}")

            if not selected_datasets:
                QMessageBox.warning(
                    self, "数据集未选择",
                    "请先在左侧参数面板选择要优化的数据集。\n\n"
                    "您可以通过以下方式选择数据集：\n"
                    "• 右键点击数据集行，选择'选择数据集'\n"
                    "• 使用参数面板顶部的数据集选择按钮"
                )
                return
            
            # 只获取选中数据集的训练数据
            training_data = {}
            all_training_sets = self.dataset_handler.get_all_training_sets()
            
            if isinstance(all_training_sets, dict):
                for dataset_name in selected_datasets:
                    if dataset_name in all_training_sets:
                        training_data[dataset_name] = all_training_sets[dataset_name]
                        print(f" 添加数据集 '{dataset_name}' 的训练数据")
                    else:
                        print(f" 警告：数据集 '{dataset_name}' 没有找到训练数据")
            else:
                print(f" 警告：训练数据格式不正确，使用所有数据")
                training_data = all_training_sets
            
            print(f" 获取选中数据集的训练数据: {type(training_data)}")
            if training_data:
                if isinstance(training_data, dict):
                    print(f" 训练数据键: {list(training_data.keys())}")
                    for key, value in training_data.items():
                        if hasattr(value, '__len__'):
                            print(f"   {key}: {len(value)} 项")
                        else:
                            print(f"   {key}: {type(value)}")
                elif isinstance(training_data, list):
                    print(f" 训练数据列表长度: {len(training_data)}")
                else:
                    print(f" 训练数据类型: {type(training_data)}")
            else:
                print(" 警告：选中的数据集没有训练数据")
                QMessageBox.warning(
                    self, "训练数据错误", 
                    f"选中的数据集 {selected_datasets} 没有可用的训练数据。\n\n"
                    "请确保数据集包含有效的训练文件。"
                )
                return

            # 创建目标函数
            print(" 正在创建目标函数...")
            objective = create_objective_function(
                training_data=training_data,
                weight_scheme=config.get('weight_scheme', 'default')
            )

            if objective is not None:
                print(" 成功创建真实数据目标函数")
            else:
                print(" 将使用模拟目标函数")

            # 创建Qt优化器
            print(" 正在创建Qt优化器...")
            self.optimizer = create_qt_optimizer(
                method=config['method'],
                parameters=parameters,
                objective=objective,
                population_size=config.get('population_size', 50),
                max_iterations=config.get('max_iterations', 200),
                tolerance=config.get('tolerance', 1e-5),
                use_quantum=config.get('use_quantum', False),
                use_ai_model=config.get('use_ai_model', False),
                parallel_threads=config.get('parallel_threads', 4)
            )

            # 验证优化器设置
            if hasattr(self.optimizer, 'worker') and hasattr(self.optimizer.worker, 'objective'):
                if self.optimizer.worker.objective is not None:
                    print(" 优化器已正确设置真实目标函数")
                else:
                    print(" 优化器将使用模拟目标函数")
            
            print(" 正在连接优化器信号...")
            # 连接优化器信号
            self.optimizer.iteration_finished.connect(self.update_optimization_progress)
            self.optimizer.optimization_finished.connect(self.optimization_finished)
            self.optimizer.status_message.connect(self.update_status_message)
            self.optimizer.progress_updated.connect(self.update_progress_bar)
            self.optimizer.error_occurred.connect(self.handle_optimization_error)

            # 启动优化
            print(" 启动优化器...")
            self.optimizer.start()
            print(" 优化器已启动")

        except Exception as e:
            print(f" 启动优化失败: {str(e)}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "优化错误", f"启动优化失败：\n{str(e)}")
            self.stop_optimization()

    def update_optimization_progress(self, iteration, loss, params):
        """更新优化进度显示 - 支持多数据集"""
        # 获取真实的损失数据
        if hasattr(self.optimizer, 'train_losses') and hasattr(self.optimizer, 'val_losses'):
            # 使用基于真实数据的损失值
            train_loss = self.optimizer.train_losses[-1] if self.optimizer.train_losses else loss
            val_loss = self.optimizer.val_losses[-1] if self.optimizer.val_losses else loss
            total_loss = self.optimizer.total_losses[-1] if self.optimizer.total_losses else loss
            
            loss_data = {
                'total': total_loss,
                'train': train_loss,
                'validation': val_loss,
                'loss': total_loss  # 向后兼容
            }
            
            print(f" 真实数据优化进度 - 迭代 {iteration}: 总损失={total_loss:.6f}, 训练={train_loss:.6f}, 验证={val_loss:.6f}")
        else:
            # 回退到原来的格式
            loss_data = {
                'total': loss,
                'train': 0,
                'validation': 0,
                'loss': loss
            }
            print(f" 优化进度 - 迭代 {iteration}: 损失={loss:.6f}")
        
        # 获取当前选中的数据集信息
        selected_datasets = self.visualization_panel.get_selected_datasets()
        print(f" 当前选中的数据集: {selected_datasets}")
        
        # 🎯 更新可视化面板 - 确保图表显示在右侧
        print(f"🎯 准备更新可视化面板，迭代{iteration}")

        if hasattr(self.visualization_panel, 'update_optimization_progress'):
            # 🎯 检查是否是增强可视化面板
            if hasattr(self.visualization_panel, 'plot_optimization_curves'):
                # 增强可视化面板 - 传递数据集名称
                selected_datasets = []
                if hasattr(self.visualization_panel, 'get_selected_datasets'):
                    selected_datasets = self.visualization_panel.get_selected_datasets()

                dataset_name = selected_datasets[0] if selected_datasets else None
                self.visualization_panel.update_optimization_progress(
                    iteration, loss_data, params, dataset_name
                )
                print(f"✅ 已更新增强可视化面板优化进度，迭代{iteration}，数据集{dataset_name}")
            else:
                # 基础可视化面板
                self.visualization_panel.update_optimization_progress(
                    iteration, loss_data, params
                )
                print(f"✅ 已更新基础可视化面板优化进度，迭代{iteration}")
        else:
            print("⚠️ 可视化面板没有update_optimization_progress方法")
            
            # 传递搜索路径历史给可视化面板以便AI分析
            if hasattr(self, 'search_path_history'):
                self.visualization_panel.search_path_history = self.search_path_history
        
        # 记录搜索路径历史（用于真实数据分析）
        if not hasattr(self, 'search_path_history'):
            self.search_path_history = []
        if not hasattr(self, 'loss_history'):
            self.loss_history = []
        if not hasattr(self, 'train_loss_history'):
            self.train_loss_history = []
        if not hasattr(self, 'val_loss_history'):
            self.val_loss_history = []
        
        # 存储真实的优化历史
        self.search_path_history.append(params.copy())
        self.loss_history.append(loss_data['total'])
        self.train_loss_history.append(loss_data['train'])
        self.val_loss_history.append(loss_data['validation'])
        
        # 更新所有可视化显示
        self.update_all_visualizations(iteration)
        
        # 输出详细的进度信息
        if iteration % 10 == 0 or iteration < 5:
            if loss_data['train'] > 0 and loss_data['validation'] > 0:
                print(f"     基于真实数据集: {current_dataset}")
                print(f"     训练损失: {loss_data['train']:.6f}, 验证损失: {loss_data['validation']:.6f}")
            else:
                print(f"     总损失: {loss_data['total']:.6f}")

    def get_current_dataset_info(self):
        """获取当前数据集信息 - 支持多数据集"""
        if hasattr(self, 'dataset_handler') and self.dataset_handler:
            structures = self.dataset_handler.get_all_structures()
            training_sets = self.dataset_handler.get_all_training_sets()
            
            # 获取数据集名称列表
            dataset_names = []
            if hasattr(self.dataset_handler, 'parameters'):
                dataset_names = list(self.dataset_handler.parameters.keys())
            
            if len(dataset_names) > 1:
                return f"多数据集优化: {len(dataset_names)}个数据集 ({', '.join(dataset_names[:3])}{'...' if len(dataset_names) > 3 else ''})"
            elif len(dataset_names) == 1:
                return f"单数据集: {dataset_names[0]}"
            else:
                return f"结构{len(structures)}个, 训练集{len(training_sets)}个"
        return "未知数据集"
    
    def on_visualization_dataset_selection_changed(self, selected_datasets):
        """处理可视化面板数据集选择变化"""
        try:
            print(f"🎯 主窗口收到数据集选择变化: {selected_datasets}")

            # 🔧 检查可视化面板是否正在进行批量操作
            if hasattr(self, 'visualization_panel'):
                viz_panel = self.visualization_panel

                # 如果正在进行全选或清空操作，不更新参数面板
                if (hasattr(viz_panel, '_selecting_all') and viz_panel._selecting_all) or \
                   (hasattr(viz_panel, '_clearing_selection') and viz_panel._clearing_selection):
                    print("🔇 可视化面板正在进行批量操作，跳过参数面板更新")
                    return

            # 只有在非批量操作时才更新参数表格
            print("📊 更新参数表格以显示选中数据集的参数")
            self.update_parameter_table_for_selected_datasets(selected_datasets)

        except Exception as e:
            print(f"❌ 处理数据集选择变化时出错: {e}")
            import traceback
            traceback.print_exc()
    
    def on_dataset_selection_changed(self, dataset_name, is_selected):
        """处理参数面板数据集选择变化"""
        try:
            print(f" 数据集选择状态变化: {dataset_name} -> {'选中' if is_selected else '取消选中'}")
            
            # 更新状态栏显示当前选中的数据集
            self.update_dataset_selection_status()
            
            # 如果有可视化面板，更新其数据集选择器
            if hasattr(self, 'visualization_panel') and hasattr(self.visualization_panel, 'update_dataset_selector'):
                self.visualization_panel.update_dataset_selector()
                
        except Exception as e:
            print(f" 处理数据集选择变化时出错: {e}")
            import traceback
            traceback.print_exc()
    
    def update_dataset_selection_status(self):
        """更新状态栏显示当前选中的数据集"""
        try:
            if hasattr(self, 'parameter_panel') and hasattr(self.parameter_panel, 'parameter_table'):
                selected_datasets = list(self.parameter_panel.parameter_table.selected_datasets)
                
                if selected_datasets:
                    if len(selected_datasets) == 1:
                        status_text = f"当前选中数据集: {selected_datasets[0]}"
                    else:
                        status_text = f"当前选中数据集: {', '.join(selected_datasets[:3])}{'...' if len(selected_datasets) > 3 else ''}"
                else:
                    status_text = "未选中任何数据集"
                
                self.status_bar.showMessage(status_text)
                
        except Exception as e:
            print(f" 更新数据集选择状态失败: {e}")
    
    def update_parameter_table_for_selected_datasets(self, selected_datasets):
        """根据选中的数据集更新参数表格"""
        try:
            print(f" 更新参数表格，选中数据集: {selected_datasets}")
            
            if not selected_datasets:
                # 没有选中数据集，显示所有可用的数据集
                print(" 没有选中数据集，显示所有可用数据集")
                if hasattr(self, 'dataset_handler') and self.dataset_handler:
                    if hasattr(self.dataset_handler, 'parameters') and self.dataset_handler.parameters:
                        all_datasets = list(self.dataset_handler.parameters.keys())
                        if all_datasets:
                            print(f" 显示所有可用数据集: {all_datasets}")
                            self._rebuild_parameter_table_with_all_datasets(all_datasets)
                            self.parameter_panel.update_selection_stats()
                            return
                
                # 如果没有任何数据集，清空参数表格
                print(" 没有任何可用数据集，清空参数表格")
                self.parameter_panel.parameter_table.setRowCount(0)
                self.parameter_panel.update_selection_stats()
                return
            
            # 检查是否有数据集处理器
            if not hasattr(self, 'dataset_handler') or not self.dataset_handler:
                print(" 没有数据集处理器")
                return
            
            # 获取选中数据集的参数
            selected_parameters = {}
            total_parameters = 0
            
            for dataset_name in selected_datasets:
                if (hasattr(self.dataset_handler, 'parameters') and 
                    self.dataset_handler.parameters and 
                    dataset_name in self.dataset_handler.parameters):
                    
                    dataset_params = self.dataset_handler.parameters[dataset_name]
                    selected_parameters[dataset_name] = dataset_params
                    total_parameters += len(dataset_params)
                    print(f" 添加数据集 '{dataset_name}' 的 {len(dataset_params)} 个参数")
            
            if not selected_parameters:
                print(" 选中的数据集没有参数")
                return
            
            # 重建参数表格
            self._rebuild_parameter_table_with_selected_datasets(selected_parameters)
            
            # 更新参数计数标签
            self.parameter_panel.update_selection_stats()
            
            print(f" 参数表格更新完成，共 {total_parameters} 个参数")
            
        except Exception as e:
            print(f" 更新参数表格失败: {e}")
            import traceback
            traceback.print_exc()
    
    def _rebuild_parameter_table_with_selected_datasets(self, selected_parameters):
        """使用选中的数据集重建参数表格"""
        try:
            table = self.parameter_panel.parameter_table
            table.setRowCount(0)  # 清空表格
            
            row_count = 0
            
            # 为每个选中的数据集添加参数
            for dataset_name, dataset_params in selected_parameters.items():
                # 添加数据集行
                row_count = self._add_dataset_row(dataset_name, dataset_params, row_count)
                
                # 添加该数据集的参数行
                for param_name, param_info in dataset_params.items():
                    row_count = self._add_parameter_row(param_name, param_info, row_count, 1, dataset_name)
            
            print(f" 参数表格重建完成，共 {row_count} 行")
            
        except Exception as e:
            print(f" 重建参数表格失败: {e}")
            import traceback
            traceback.print_exc()
    
    def _rebuild_parameter_table_with_all_datasets(self, all_datasets):
        """使用所有可用数据集重建参数表格"""
        try:
            table = self.parameter_panel.parameter_table
            table.setRowCount(0)  # 清空表格
            
            row_count = 0
            
            # 为每个可用数据集添加参数
            for dataset_name in all_datasets:
                if (hasattr(self.dataset_handler, 'parameters') and 
                    self.dataset_handler.parameters and 
                    dataset_name in self.dataset_handler.parameters):
                    
                    dataset_params = self.dataset_handler.parameters[dataset_name]
                    
                    # 添加数据集行
                    row_count = self._add_dataset_row(dataset_name, dataset_params, row_count)
                    
                    # 添加该数据集的参数行
                    for param_name, param_info in dataset_params.items():
                        row_count = self._add_parameter_row(param_name, param_info, row_count, 1, dataset_name)
            
            print(f" 参数表格重建完成，显示所有数据集，共 {row_count} 行")
            
        except Exception as e:
            print(f" 重建参数表格失败: {e}")
            import traceback
            traceback.print_exc()

    def update_all_visualizations(self, iteration):
        """更新所有可视化显示，基于真实优化数据"""
        try:
            # 更新参数空间搜索路径
            if hasattr(self, 'search_path_history') and len(self.search_path_history) > 1:
                # 获取当前最优参数
                min_loss_idx = np.argmin(self.loss_history) if self.loss_history else -1
                current_best = self.search_path_history[min_loss_idx] if min_loss_idx >= 0 else self.search_path_history[-1]
                
                # 更新参数空间可视化
                if hasattr(self.visualization_panel, 'update_parameter_space'):
                    self.visualization_panel.update_parameter_space(self.search_path_history, current_best)
                
                print(f" 已更新参数空间搜索轨迹，共{len(self.search_path_history)}个点")
            
            # 更新帕累托前沿（如果进行多目标优化）
            if hasattr(self, 'train_loss_history') and hasattr(self, 'val_loss_history'):
                if len(self.train_loss_history) > 0 and len(self.val_loss_history) > 0:
                    # 创建多目标数据用于帕累托前沿
                    solutions = self.search_path_history[-20:] if len(self.search_path_history) > 20 else self.search_path_history
                    objectives = list(zip(
                        self.train_loss_history[-20:] if len(self.train_loss_history) > 20 else self.train_loss_history,
                        self.val_loss_history[-20:] if len(self.val_loss_history) > 20 else self.val_loss_history
                    ))
                    
                    if hasattr(self.visualization_panel, 'update_pareto_front'):
                        # 将objectives转换为pareto_points格式 
                        pareto_points = objectives  # objectives已经是(obj1, obj2)元组的列表
                        self.visualization_panel.update_pareto_front(pareto_points)
                        print(f" 已更新帕累托前沿，基于{len(pareto_points)}个解")
            
            # 输出当前优化状态
            if len(self.loss_history) > 0:
                current_loss = self.loss_history[-1]
                best_loss = min(self.loss_history)
                improvement = ((self.loss_history[0] - best_loss) / self.loss_history[0] * 100) if len(self.loss_history) > 1 and self.loss_history[0] > 0 else 0
                
                print(f" 已更新所有可视化：第{iteration}次迭代，当前损失{current_loss:.6f}，最佳损失{best_loss:.6f}，改进{improvement:.1f}%")
        
        except Exception as e:
            print(f" 更新可视化时出错: {e}")

    def optimization_finished(self, result):
        """优化完成处理"""
        self.stop_opt_act.setEnabled(False)
        self.parameter_panel.start_button.setEnabled(True)
        self.parameter_panel.stop_button.setEnabled(False)
        
        # 显示结果
        if result.get('success', False):
            message = f"优化成功完成！\n"
            message += f"最终损失: {result['best_loss']:.6f}\n"
            message += f"迭代次数: {result['iterations']}\n"
            message += f"优化方法: {result['method']}"
            
            # 添加搜索路径统计信息
            if hasattr(self, 'search_path_history') and self.search_path_history:
                message += f"\n搜索路径点数: {len(self.search_path_history)}"
                
                # 计算参数变化幅度
                if len(self.search_path_history) > 1:
                    first_params = self.search_path_history[0]
                    last_params = self.search_path_history[-1]
                    
                    param_changes = []
                    for key in first_params:
                        if key in last_params:
                            change = abs(last_params[key] - first_params[key]) / abs(first_params[key] + 1e-10)
                            param_changes.append(change)
                    
                    if param_changes:
                        avg_change = np.mean(param_changes) * 100
                        message += f"\n平均参数变化: {avg_change:.2f}%"
            
            QMessageBox.information(self, "优化完成", message)
            
            # 更新参数表格显示最优参数
            if 'best_params' in result:
                self._update_parameter_table_with_optimized_values(result['best_params'])
        else:
            QMessageBox.warning(self, "优化失败", "优化过程未能成功完成")
        
        # 保存搜索路径数据供后续分析
        if hasattr(self, 'search_path_history'):
            print(f" 优化完成，共记录 {len(self.search_path_history)} 个搜索路径点")

    def update_status_message(self, message):
        """更新状态消息"""
        self.status_bar.showMessage(message)
        print(f" 优化状态: {message}")

    def update_progress_bar(self, percentage):
        """更新进度条"""
        self.progress_label.setText(f"进度: {percentage}%")

    def handle_optimization_error(self, error_message):
        """处理优化错误"""
        QMessageBox.critical(self, "优化错误", f"优化过程中发生错误：\n{error_message}")
        self.stop_optimization()

    def _update_parameter_table_with_optimized_values(self, optimized_params):
        """用优化后的参数值更新参数表格"""
        try:
            for row in range(self.parameter_panel.parameter_table.rowCount()):
                param_name = self.parameter_panel.parameter_table.item(row, 0).text()
                if param_name in optimized_params:
                    # 更新参数值
                    new_value = optimized_params[param_name]
                    value_item = QTableWidgetItem(f"{new_value:.4f}")
                    value_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                    self.parameter_panel.parameter_table.setItem(row, 1, value_item)
            
            print(" 参数表格已更新为优化后的值")
        except Exception as e:
            print(f" 更新参数表格时出错: {e}")

    def start_optimization_from_menu(self):
        """从菜单启动优化"""
        try:
            if hasattr(self, 'parameter_panel') and self.parameter_panel:
                if hasattr(self.parameter_panel, 'parameter_table'):
                    self.parameter_panel.parameter_table.start_optimization()
                else:
                    QMessageBox.warning(self, "警告", "参数表格不可用")
            else:
                QMessageBox.warning(self, "警告", "参数面板不可用")
        except Exception as e:
            print(f" 从菜单启动优化失败: {e}")
            QMessageBox.critical(self, "错误", f"启动优化失败：\n{str(e)}")

    def stop_optimization(self):
        """停止优化过程"""
        if hasattr(self, 'optimizer') and self.optimizer:
            self.optimizer.stop()
            
        self.stop_opt_act.setEnabled(False)
        self.parameter_panel.start_button.setEnabled(True)
        self.parameter_panel.stop_button.setEnabled(False)
        self.status_bar.showMessage("优化已停止")

    def simulate_optimization_update(self):
        """模拟优化过程更新"""
        self.sim_iteration += 1

        # 更新进度显示
        progress = min(100, int(self.sim_iteration / 50 * 100))
        self.progress_label.setText(f"进度: {progress}%")

        # 更新可视化
        import numpy as np

        # 生成模拟数据
        iterations = np.arange(self.sim_iteration)
        values = 100 * np.exp(-0.05 * iterations) + 10 * np.exp(-0.1 * iterations) * np.sin(0.5 * iterations) + 2

        # 更新优化可视化
        self.visualization_panel.update_optimization_plot(iterations, values)

        # 当达到一定迭代次数时停止
        if self.sim_iteration >= 50:
            self.stop_optimization()
            self.status_bar.showMessage("优化完成")

    def run_sensitivity_analysis(self):
        """运行参数敏感性分析"""
        try:
            # 检查是否已导入数据集
            if not hasattr(self, 'dataset_handler') or self.dataset_handler is None:
                QMessageBox.warning(
                    self, "数据集未导入", 
                    "请先导入数据集再进行敏感性分析。\n\n"
                    "您可以通过菜单 '文件' > '导入' 来导入数据集。"
                )
                return
            
            # 检查数据处理器是否有参数
            if not hasattr(self.dataset_handler, 'parameters') or not self.dataset_handler.parameters:
                print(f" 数据集处理器状态检查:")
                print(f"   - dataset_handler 存在: {hasattr(self, 'dataset_handler')}")
                print(f"   - dataset_handler 类型: {type(self.dataset_handler) if hasattr(self, 'dataset_handler') else 'None'}")
                if hasattr(self, 'dataset_handler') and self.dataset_handler:
                    print(f"   - 有 parameters 属性: {hasattr(self.dataset_handler, 'parameters')}")
                    if hasattr(self.dataset_handler, 'parameters'):
                        print(f"   - parameters 内容: {self.dataset_handler.parameters}")
                
                QMessageBox.warning(
                    self, "参数错误", 
                    "数据处理器中没有可分析的参数。\n\n"
                    "请确保数据集包含有效的参数文件。"
                )
                return
            
            # 检查参数表格
            if self.parameter_panel.parameter_table.rowCount() == 0:
                QMessageBox.warning(self, "参数错误", "当前没有可分析的参数")
                return
            
            # 检查是否有选中的数据集 - 修复：检查左侧参数面板选中的数据集
            selected_datasets = list(self.parameter_panel.parameter_table.selected_datasets)
            if not selected_datasets:
                QMessageBox.warning(
                    self, "数据集未选择",
                    "请先在左侧选择要分析的数据集再进行敏感性分析。\n\n"
                    "您可以在左侧参数表格中勾选数据集复选框来选择要分析的数据集。"
                )
                return
            
            print(f" 敏感性分析将基于选中的数据集: {selected_datasets}")
            
            # 检查是否有选中的参数
            selected_parameters = self.parameter_panel.parameter_table.get_parameters_for_optimization()
            
            if not selected_parameters:
                QMessageBox.warning(
                    self, "参数未选择", 
                    "请先勾选要分析的参数再进行敏感性分析。\n\n"
                    "您可以通过以下方式选择参数：\n"
                    "• 手动勾选参数行前的复选框\n"
                    "• 点击'全选'按钮选择所有参数\n"
                    "• 点击'选择数据集参数'按钮选择特定数据集的参数"
                )
                return
            
            print(f" 开始敏感性分析，选中参数: {list(selected_parameters.keys())}")
            
            # 创建进度对话框
            progress = QProgressDialog("正在进行参数敏感性分析...", "取消", 0, 100, self)
            progress.setWindowModality(Qt.WindowModal)
            progress.setWindowTitle("敏感性分析")
            progress.show()
            
            # 模拟敏感性分析过程
            sensitivities = {}
            param_count = 0
            total_params = len(selected_parameters)
            
            print(f" 开始分析 {total_params} 个选中参数")
            
            # 只分析选中的参数
            for param_name, param_info in selected_parameters.items():
                # 模拟敏感性计算
                import random
                random.seed(hash(param_name) % 1000)  # 确保结果一致
                sensitivity = random.uniform(0.1, 0.9)
                sensitivities[param_name] = sensitivity
                
                param_count += 1
                progress_value = int((param_count / max(total_params, 1)) * 100)
                progress.setValue(progress_value)
                progress.setLabelText(f"分析参数: {param_name}")
                
                if progress.wasCanceled():
                    break
                
                # 模拟计算时间
                QApplication.processEvents()
            
            progress.setValue(100)
            
            # 🎨 更新参数表格中的敏感性值和颜色
            if sensitivities:
                print(f"📊 更新参数面板敏感性显示，共 {len(sensitivities)} 个参数")

                # 调用参数面板的更新方法（包含颜色更新）
                self.parameter_panel.parameter_table.update_sensitivities(sensitivities)

                print("✅ 参数面板敏感性颜色已更新")
                
                #  更新可视化面板显示敏感性分析结果
                if hasattr(self, 'visualization_panel'):
                    param_names = list(sensitivities.keys())
                    sensitivity_values = list(sensitivities.values())
                    
                    # 更新可视化面板的敏感性数据
                    self.visualization_panel.sensitivity_data['parameters'] = param_names
                    self.visualization_panel.sensitivity_data['sensitivities'] = sensitivity_values
                    
                    # 绘制敏感性分析图表
                    try:
                        self.visualization_panel.plot_sensitivity_analysis(param_names, sensitivity_values)
                        print(f" 敏感性分析图表已更新: {len(param_names)} 个参数")
                        
                        # 提示用户查看选中的数据集
                        selected_datasets = self.visualization_panel.get_selected_datasets()
                        if selected_datasets:
                            print(f" 敏感性分析基于选中的数据集: {selected_datasets}")
                        else:
                            print(" 建议在右侧选择数据集以查看对应的敏感性分析")
                            
                    except Exception as e:
                        print(f" 更新敏感性分析图表时出错: {e}")
                
                # 🎯 自动切换到敏感性分析标签页并确保图表显示
                if hasattr(self, 'visualization_panel') and hasattr(self.visualization_panel, 'tab_widget'):
                    try:
                        # 找到敏感性分析标签页的索引
                        sensitivity_tab_found = False
                        for i in range(self.visualization_panel.tab_widget.count()):
                            tab_text = self.visualization_panel.tab_widget.tabText(i)
                            if "敏感性" in tab_text or "sensitivity" in tab_text.lower():
                                self.visualization_panel.tab_widget.setCurrentIndex(i)
                                sensitivity_tab_found = True
                                print(f"🎯 已自动切换到敏感性分析选项卡: {tab_text}")
                                break

                        if not sensitivity_tab_found:
                            print("⚠️ 未找到敏感性分析选项卡")

                    except Exception as e:
                        print(f"❌ 切换到敏感性分析标签页时出错: {e}")

                # 🎯 同时更新增强可视化面板（如果存在）
                if hasattr(self, 'enhanced_visualization_panel') and self.enhanced_visualization_panel:
                    try:
                        self.enhanced_visualization_panel.plot_sensitivity_analysis(
                            list(sensitivities.keys()),
                            list(sensitivities.values())
                        )
                        print("✅ 增强可视化面板敏感性图表已更新")
                    except Exception as e:
                        print(f"⚠️ 更新增强可视化面板敏感性图表失败: {e}")
            
            progress.close()
            
            # 显示结果摘要
            if sensitivities:
                high_sensitivity = [name for name, val in sensitivities.items() if val > 0.7]
                medium_sensitivity = [name for name, val in sensitivities.items() if 0.4 < val <= 0.7]
                low_sensitivity = [name for name, val in sensitivities.items() if val <= 0.4]
                
                result_msg = f"参数敏感性分析完成！\n\n"
                result_msg += f"分析参数数量: {len(sensitivities)} 个\n"
                result_msg += f"高敏感性参数 (>0.7): {len(high_sensitivity)} 个\n"
                result_msg += f"中等敏感性参数 (0.4-0.7): {len(medium_sensitivity)} 个\n"
                result_msg += f"低敏感性参数 (≤0.4): {len(low_sensitivity)} 个\n\n"
                result_msg += f"建议优先优化高敏感性参数以获得更好的效果。\n"
                result_msg += f"图表已更新到右侧的'敏感性分析'标签页。\n\n"
                result_msg += f" 提示：敏感性分析基于您选中的参数，如需分析其他参数请重新勾选。"
                
                QMessageBox.information(self, "敏感性分析完成", result_msg)
                
                print(f" 敏感性分析完成：{len(sensitivities)} 个参数")
                print(f"   高敏感性: {high_sensitivity}")
                print(f"   中等敏感性: {medium_sensitivity[:3]}{'...' if len(medium_sensitivity) > 3 else ''}")
            else:
                QMessageBox.warning(self, "分析失败", "未能计算参数敏感性")
            
        except Exception as e:
            print(f" 敏感性分析失败: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "错误", f"敏感性分析失败: {str(e)}")

    def run_ai_generation(self):
        """运行生成式AI参数预测"""
        try:
            # 创建AI参数生成对话框
            dialog = QDialog(self)
            dialog.setWindowTitle("生成式AI参数预测")
            dialog.setFixedSize(400, 300)
            
            layout = QVBoxLayout(dialog)
            
            # 添加说明
            info_label = QLabel("基于当前数据集特征，AI将预测最优参数范围")
            layout.addWidget(info_label)
            
            # 添加选项
            optimization_combo = QComboBox()
            optimization_combo.addItems([
                "快速收敛模式",
                "高精度模式", 
                "平衡模式",
                "实验探索模式"
            ])
            layout.addWidget(QLabel("优化模式:"))
            layout.addWidget(optimization_combo)
            
            # 添加材料类型选择
            material_combo = QComboBox()
            material_combo.addItems([
                "金属材料",
                "陶瓷材料",
                "聚合物材料",
                "复合材料",
                "自动检测"
            ])
            layout.addWidget(QLabel("材料类型:"))
            layout.addWidget(material_combo)
            
            # 按钮
            button_layout = QHBoxLayout()
            generate_btn = QPushButton("生成参数")
            cancel_btn = QPushButton("取消")
            
            button_layout.addWidget(generate_btn)
            button_layout.addWidget(cancel_btn)
            layout.addLayout(button_layout)
            
            # 连接信号
            def generate_params():
                # 模拟AI参数生成
                progress = QProgressDialog("AI正在分析数据集...", "取消", 0, 100, dialog)
                progress.setWindowModality(Qt.WindowModal)
                
                for i in range(101):
                    progress.setValue(i)
                    QApplication.processEvents()
                    QThread.msleep(20)  # 模拟计算时间
                    
                    if progress.wasCanceled():
                        return
                
                progress.close()
                
                # 生成建议参数
                suggestions = {
                    'p_2_1': (1.8, 2.2),
                    'p_2_2': (1.2, 1.8),
                    'p_3_1': (80, 120),
                    'p_3_2': (40, 60),
                    'p_4_1': (0.3, 0.7)
                }
                
                # 显示结果
                result_text = "AI建议的参数范围:\n\n"
                for param, (min_val, max_val) in suggestions.items():
                    result_text += f"{param}: [{min_val:.2f}, {max_val:.2f}]\n"
                
                result_text += "\n点击'应用建议'将更新参数表格"
                
                msg = QMessageBox(dialog)
                msg.setWindowTitle("AI参数预测结果")
                msg.setText(result_text)
                msg.addButton("应用建议", QMessageBox.AcceptRole)
                msg.addButton("关闭", QMessageBox.RejectRole)
                
                if msg.exec_() == 0:  # 应用建议
                    self.apply_ai_suggestions(suggestions)
                
                dialog.accept()
            
            generate_btn.clicked.connect(generate_params)
            cancel_btn.clicked.connect(dialog.reject)
            
            dialog.exec_()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"AI参数生成失败: {str(e)}")

    def apply_ai_suggestions(self, suggestions):
        """应用AI建议的参数 - 修复版本，正确应用到参数表格"""
        try:
            if not hasattr(self.parameter_panel, 'parameter_table'):
                QMessageBox.warning(self, "错误", "参数表格未初始化")
                return

            table = self.parameter_panel.parameter_table
            applied_count = 0

            print(f"🤖 开始应用AI建议，共 {len(suggestions)} 个参数")

            for row in range(table.rowCount()):
                try:
                    # 获取参数名（第1列）
                    name_item = table.item(row, 1)
                    if not name_item:
                        continue

                    # 检查是否是参数行
                    user_data = name_item.data(Qt.UserRole)
                    if not user_data or user_data.get('type') != 'parameter':
                        continue

                    param_name = user_data.get('name', '')
                    if not param_name:
                        # 回退到文本解析
                        text = name_item.text()
                        if '└─' in text:
                            param_name = text.split('└─')[-1].strip()
                        else:
                            continue

                    # 检查是否在AI建议中
                    if param_name in suggestions:
                        min_val, max_val = suggestions[param_name]
                        current_val = (min_val + max_val) / 2  # 使用中间值

                        # 更新当前值（第2列）
                        value_item = table.item(row, 2)
                        if value_item:
                            value_item.setText(f"{current_val:.4f}")

                        # 更新范围（第3列）
                        range_item = table.item(row, 3)
                        if range_item:
                            range_item.setText(f"{min_val:.2f} - {max_val:.2f}")

                        applied_count += 1
                        print(f"   ✅ 应用参数 {param_name}: {current_val:.4f} (范围: {min_val:.2f}-{max_val:.2f})")

                except Exception as e:
                    print(f"   ❌ 跳过行 {row}: {e}")
                    continue

            if applied_count > 0:
                QMessageBox.information(self, "完成", f"AI建议已应用到参数表格\n成功应用 {applied_count} 个参数")
                # 刷新表格显示
                table.viewport().update()
            else:
                QMessageBox.warning(self, "警告", "没有找到匹配的参数，请检查参数名称")

        except Exception as e:
            QMessageBox.warning(self, "警告", f"应用AI建议时出错: {str(e)}")
            print(f"❌ 应用AI建议失败: {e}")

    def run_multi_objective_optimization(self):
        """运行多目标优化"""
        try:
            # 检查是否有选中的数据集 - 基于左侧参数面板
            selected_datasets = list(self.parameter_panel.parameter_table.selected_datasets)
            if not selected_datasets:
                QMessageBox.warning(
                    self, "数据集未选择",
                    "请先在左侧选择要优化的数据集再进行多目标优化。\n\n"
                    "您可以在左侧参数表格中勾选数据集复选框来选择要优化的数据集。"
                )
                return

            print(f"🎯 多目标优化将基于选中的数据集: {selected_datasets}")

            # 🔥 关键修复：检查是否有选中的参数
            selected_params = self.parameter_panel.parameter_table.get_parameters_for_optimization()
            if not selected_params:
                QMessageBox.warning(
                    self, "参数未选择",
                    "请先在左侧参数表格中选择要优化的参数！\n\n"
                    "操作步骤：\n"
                    "1. 在左侧参数表格中勾选数据集\n"
                    "2. 勾选要优化的参数（优化列的复选框）\n"
                    "3. 再执行多目标优化"
                )
                return

            print(f"✅ 已选择 {len(selected_params)} 个参数进行多目标优化")

            # 创建多目标优化对话框
            dialog = QDialog(self)
            dialog.setWindowTitle(f"多目标优化设置 - 数据集: {', '.join(selected_datasets)}")
            dialog.setFixedSize(500, 450)
            
            layout = QVBoxLayout(dialog)
            
            # 目标函数选择
            layout.addWidget(QLabel("选择优化目标:"))
            
            energy_cb = QCheckBox("能量RMSE")
            energy_cb.setChecked(True)
            force_cb = QCheckBox("力RMSE")
            force_cb.setChecked(True)
            charge_cb = QCheckBox("电荷误差")
            geometry_cb = QCheckBox("几何结构误差")
            
            layout.addWidget(energy_cb)
            layout.addWidget(force_cb)
            layout.addWidget(charge_cb)
            layout.addWidget(geometry_cb)
            
            # 优化算法选择
            layout.addWidget(QLabel("优化算法:"))
            algorithm_combo = QComboBox()
            algorithm_combo.addItems([
                "NSGA-II (非支配排序遗传算法)",
                "MOEA/D (基于分解的多目标进化算法)",
                "SPEA2 (强度帕累托进化算法)",
                "简化版多目标PSO"
            ])
            layout.addWidget(algorithm_combo)
            
            # 参数设置
            layout.addWidget(QLabel("算法参数:"))
            
            param_layout = QFormLayout()
            population_spin = QSpinBox()
            population_spin.setRange(20, 200)
            population_spin.setValue(50)
            param_layout.addRow("种群大小:", population_spin)
            
            generations_spin = QSpinBox()
            generations_spin.setRange(10, 500)
            generations_spin.setValue(100)
            param_layout.addRow("迭代次数:", generations_spin)
            
            layout.addLayout(param_layout)
            
            # 按钮
            button_layout = QHBoxLayout()
            start_btn = QPushButton("开始优化")
            cancel_btn = QPushButton("取消")
            
            button_layout.addWidget(start_btn)
            button_layout.addWidget(cancel_btn)
            layout.addLayout(button_layout)
            
            def start_multi_optimization():
                dialog.accept()
                self.run_multi_objective_simulation(
                    population_spin.value(),
                    generations_spin.value(),
                    [energy_cb.isChecked(), force_cb.isChecked(), 
                     charge_cb.isChecked(), geometry_cb.isChecked()]
                )
            
            start_btn.clicked.connect(start_multi_optimization)
            cancel_btn.clicked.connect(dialog.reject)
            
            dialog.exec_()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"多目标优化设置失败: {str(e)}")

    def run_multi_objective_simulation(self, population_size, generations, objectives):
        """运行多目标优化模拟"""
        try:
            # 创建进度对话框
            progress = QProgressDialog("多目标优化进行中...", "停止", 0, generations, self)
            progress.setWindowModality(Qt.WindowModal)
            
            # 模拟多目标优化过程
            solutions = []
            objective_values = []
            
            for gen in range(generations):
                progress.setValue(gen)
                progress.setLabelText(f"第 {gen+1}/{generations} 代优化中...")
                QApplication.processEvents()
                
                if progress.wasCanceled():
                    break
                
                # 生成一代解
                gen_solutions = []
                gen_objectives = []
                
                for _ in range(population_size // 10):  # 每代保存部分解
                    # 模拟解
                    solution = {
                        'p_2_1': np.random.uniform(1.5, 2.5),
                        'p_2_2': np.random.uniform(1.0, 2.0),
                        'p_3_1': np.random.uniform(80, 120)
                    }
                    
                    # 模拟目标函数值
                    obj_vals = []
                    if objectives[0]:  # 能量RMSE
                        obj_vals.append(np.random.uniform(0.1, 1.0))
                    if objectives[1]:  # 力RMSE  
                        obj_vals.append(np.random.uniform(0.2, 1.2))
                    if objectives[2]:  # 电荷误差
                        obj_vals.append(np.random.uniform(0.05, 0.5))
                    if objectives[3]:  # 几何结构误差
                        obj_vals.append(np.random.uniform(0.1, 0.8))
                    
                    gen_solutions.append(solution)
                    gen_objectives.append(obj_vals)
                
                solutions.extend(gen_solutions)
                objective_values.extend(gen_objectives)
                
                # 模拟计算时间
                QThread.msleep(50)
            
            progress.close()
            
            # 更新帕累托前沿可视化 - 修复参数传递
            try:
                if hasattr(self.visualization_panel, 'update_pareto_front'):
                    # 转换格式：从列表的列表转换为元组的列表
                    pareto_points = []
                    for obj_list in objective_values:
                        if len(obj_list) >= 2:
                            pareto_points.append((obj_list[0], obj_list[1]))
                    
                    if pareto_points:
                        self.visualization_panel.update_pareto_front(pareto_points)
                
                if hasattr(self, 'enhanced_viz_panel'):
                    # 增强可视化面板需要不同的格式
                    self.enhanced_viz_panel.update_multi_objective_data(solutions, objective_values)
                    
            except Exception as e:
                print(f" 更新可视化时出错: {e}")
            
            # 启用帕累托前沿绘制按钮
            self.plot_pareto_front_act.setEnabled(True)
            
            # 自动绘制帕累托前沿
            try:
                self.plot_pareto_front()
                print(" 自动绘制帕累托前沿完成")
            except Exception as e:
                print(f" 自动绘制帕累托前沿失败: {e}")
            
            QMessageBox.information(self, "完成", 
                                  f"多目标优化完成!\n"
                                  f"生成了 {len(solutions)} 个解\n"
                                  f"帕累托前沿已更新\n\n"
                                  f"💡 提示：切换到'学术级可视化'选项卡查看详细图表")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"多目标优化失败: {str(e)}")

    def run_multi_fidelity_optimization(self):
        """运行多保真度优化"""
        try:
            # 检查是否有选中的数据集 - 基于左侧参数面板
            selected_datasets = list(self.parameter_panel.parameter_table.selected_datasets)
            if not selected_datasets:
                QMessageBox.warning(
                    self, "数据集未选择",
                    "请先在左侧选择要优化的数据集再进行多保真度优化。\n\n"
                    "您可以在左侧参数表格中勾选数据集复选框来选择要优化的数据集。"
                )
                return

            print(f"🎯 多保真度优化将基于选中的数据集: {selected_datasets}")

            # 检查是否已导入数据集
            if not hasattr(self, 'dataset_handler') or self.dataset_handler is None:
                QMessageBox.warning(
                    self, "数据集未导入",
                    "请先导入数据集再开始多保真度优化。\n\n"
                    "您可以通过菜单 '文件' > '导入' 来导入数据集。"
                )
                return

            # 检查是否有选中的参数
            selected_params = self.parameter_panel.parameter_table.get_parameters_for_optimization()
            if not selected_params:
                QMessageBox.warning(self, "参数错误", "请先选择要优化的参数")
                return
            
            # 询问用户是否继续（因为需要JAX等依赖）
            reply = QMessageBox.question(
                self, "多保真度优化", 
                "多保真度优化功能需要JAX等高级库支持。\n\n"
                "如果缺少依赖库，将使用简化版本运行。\n\n"
                "是否继续？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )
            
            if reply == QMessageBox.No:
                return
            
            try:
                # 尝试导入多保真度优化器
                from optimizer.multi_objective import MultiFidelityOptimizer
                
                # 创建低保真度和高保真度评估函数 - 修复参数签名
                def low_fidelity_func(params, data=None):
                    """低保真度评估：快速但不够精确"""
                    # 使用简化的计算
                    if isinstance(params, dict):
                        return sum(abs(p) for p in params.values()) + 0.1 * np.random.random()
                    else:
                        return 1.0  # 错误时返回默认值
                
                def high_fidelity_func(params, data=None):
                    """高保真度评估：精确但计算昂贵"""
                    # 使用完整的ReaxFF计算（这里用模拟）
                    try:
                        from optimizer.objectives import create_objective_function
                        
                        # 如果有真实数据，使用真实目标函数
                        if data and hasattr(self, 'dataset_handler') and self.dataset_handler:
                            objective_func = create_objective_function(
                                data.get('structures', {}),
                                data.get('training_sets', {})
                            )
                            if objective_func:
                                return objective_func(params)
                        
                        # 回退到简化计算
                        if isinstance(params, dict):
                            return sum(p**2 for p in params.values()) + 0.05 * np.random.random()
                        else:
                            return 1.0  # 错误时返回默认值
                            
                    except Exception as e:
                        print(f" 高保真度评估错误: {e}")
                        if isinstance(params, dict):
                            return sum(p**2 for p in params.values()) + 0.05 * np.random.random()
                        else:
                            return 1.0
                
                # 创建多保真度优化器
                optimizer = MultiFidelityOptimizer(
                    low_fidelity_func=low_fidelity_func,
                    high_fidelity_func=high_fidelity_func,
                    correlation_threshold=0.7
                )
                
                # 准备初始参数
                initial_params = {}
                for name, param_info in selected_params.items():
                    if isinstance(param_info, dict) and 'value' in param_info:
                        initial_params[name] = param_info['value']
                    elif isinstance(param_info, (int, float)):
                        initial_params[name] = param_info
                    else:
                        # 如果无法获取值，使用默认值
                        initial_params[name] = 1.0
                
                print(f" 多保真度优化初始参数: {len(initial_params)} 个参数")
                for name, value in list(initial_params.items())[:5]:  # 只显示前5个
                    print(f"   {name}: {value}")
                
                # 获取训练数据
                training_data = {
                    'structures': self.dataset_handler.get_all_structures(),
                    'training_sets': self.dataset_handler.get_all_training_sets()
                }
                
                # 显示进度对话框
                progress = QProgressDialog("正在执行多保真度优化...", "停止", 0, 50, self)
                progress.setWindowModality(Qt.WindowModal)
                progress.setWindowTitle("多保真度优化")
                progress.show()
                
                # 执行优化
                result = optimizer.optimize(
                    initial_params=initial_params,
                    data=training_data,
                    budget=50,  # 计算预算
                    initial_high_fidelity=5
                )
                
                progress.setValue(50)
                
                # 显示结果
                message = f"多保真度优化完成！\n\n"
                message += f"最优参数:\n"
                for name, value in result['best_params'].items():
                    message += f"  {name}: {value:.6f}\n"
                message += f"\n最优损失: {result['best_loss']:.6f}\n"
                message += f"低保真度评估次数: {len(optimizer.low_fidelity_history)}\n"
                message += f"高保真度评估次数: {len(optimizer.high_fidelity_history)}\n"
                message += f"计算效率提升: {len(optimizer.low_fidelity_history) / max(1, len(optimizer.high_fidelity_history)):.1f}倍"
                
                QMessageBox.information(self, "优化完成", message)
                
                # 更新参数表格
                self._update_parameter_table_with_optimized_values(result['best_params'])
                
                progress.close()
                
            except ImportError as e:
                # 如果缺少依赖，提供简化版本
                QMessageBox.warning(
                    self, "依赖缺失", 
                    f"多保真度优化需要额外的依赖库：\n{str(e)}\n\n"
                    "请安装必要的依赖库或使用标准优化功能。\n\n"
                    "需要的库包括：jax, flax, optax 等"
                )
                
        except Exception as e:
            print(f" 多保真度优化失败: {e}")
            QMessageBox.critical(self, "优化失败", f"多保真度优化过程中发生错误：\n{str(e)}")

    def plot_pareto_front(self):
        """绘制帕累托前沿"""
        try:
            # 检查是否有可视化数据
            has_data = False
            
            if hasattr(self, 'enhanced_viz_panel'):
                # 尝试使用增强可视化面板
                if hasattr(self.enhanced_viz_panel, 'plot_pareto_front_compat'):
                    self.enhanced_viz_panel.plot_pareto_front_compat()
                    has_data = True
                    print(" 使用增强可视化面板绘制帕累托前沿")
                elif hasattr(self.enhanced_viz_panel, 'plot_pareto_front'):
                    self.enhanced_viz_panel.plot_pareto_front()
                    has_data = True
                    print(" 使用增强可视化面板绘制帕累托前沿(备用方法)")
            
            if not has_data and hasattr(self.visualization_panel, 'plot_pareto_front'):
                # 使用基础可视化面板
                self.visualization_panel.plot_pareto_front()
                has_data = True
                print(" 使用基础可视化面板绘制帕累托前沿")
            
            if not has_data:
                # 如果没有数据，生成示例帕累托前沿
                self._generate_demo_pareto_front()
                
        except Exception as e:
            print(f" 绘制帕累托前沿失败: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "错误", f"绘制帕累托前沿失败: {str(e)}")
    
    def _generate_demo_pareto_front(self):
        """生成演示帕累托前沿"""
        try:
            print(" 生成演示帕累托前沿...")
            
            # 生成示例多目标优化数据
            solutions = []
            objective_values = []
            
            for i in range(20):
                # 生成示例解
                solution = {
                    'p_2_1': 1.5 + 0.5 * np.random.random(),
                    'p_2_2': 1.0 + 0.5 * np.random.random(),
                    'p_3_1': 80 + 40 * np.random.random()
                }
                solutions.append(solution)
                
                # 生成目标函数值（能量RMSE vs 力RMSE）
                energy_rmse = 0.1 + 0.9 * np.random.random()
                force_rmse = 0.05 + 0.45 * np.random.random()
                objective_values.append([energy_rmse, force_rmse])
            
            # 更新可视化
            if hasattr(self, 'enhanced_viz_panel'):
                self.enhanced_viz_panel.update_multi_objective_data(solutions, objective_values)
                print(" 演示帕累托前沿数据已更新到增强可视化面板")
            
            # 也更新基础可视化面板
            if hasattr(self.visualization_panel, 'update_pareto_front'):
                pareto_points = [(obj[0], obj[1]) for obj in objective_values]
                self.visualization_panel.update_pareto_front(pareto_points)
                print(" 演示帕累托前沿数据已更新到基础可视化面板")
            
            QMessageBox.information(
                self, "帕累托前沿", 
                "演示帕累托前沿已生成！\n\n"
                "显示了20个模拟解的能量RMSE vs 力RMSE权衡关系。\n"
                "请切换到'学术级可视化'选项卡查看详细图表。"
            )
            
        except Exception as e:
            print(f" 生成演示帕累托前沿失败: {e}")
            QMessageBox.warning(self, "警告", f"生成演示帕累托前沿失败: {str(e)}")

    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(
            self,
            "关于ReaxFFOpt",
            "ReaxFFOpt - 先进的反应力场参数优化框架\n\n"
            "版本: 1.0.0\n"
            "开发团队: ReaxFFOpt团队\n\n"
            "基于多模态科学大模型、高效搜索路径和量子计算协同优化"
        )

    def show_help(self):
        """显示帮助文档"""
        QMessageBox.information(
            self,
            "帮助文档",
            "帮助文档尚未实现。请访问项目主页获取更多信息。"
        )

    def _generate_pareto_front_data(self):
        """生成帕累托前沿数据（能量误差 vs 力误差）"""
        pareto_points = []
        
        # 基于当前损失历史生成能量-力误差的帕累托前沿
        for i in range(min(20, len(self.train_loss_history))):  # 最多20个点
            idx = len(self.train_loss_history) - 1 - i
            
            # 模拟能量RMSE和力RMSE
            train_loss = self.train_loss_history[idx]
            val_loss = self.val_loss_history[idx]
            
            # 从总损失分解出能量和力的贡献
            energy_rmse = train_loss * 0.7 + 0.01 * np.random.random()  # 能量误差
            force_rmse = val_loss * 0.8 + 0.005 * np.random.random()   # 力误差
            
            pareto_points.append({
                'objective1': energy_rmse,
                'objective2': force_rmse
            })
        
        return pareto_points

    def save_force_field(self):
        """保存力场文件"""
        try:
            # 打开文件对话框选择保存位置
            save_dir = QFileDialog.getExistingDirectory(
                self, "选择保存目录", "",
                QFileDialog.ShowDirsOnly | QFileDialog.DontResolveSymlinks
            )

            if save_dir:
                # 创建时间戳文件夹
                from datetime import datetime
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                result_dir = os.path.join(save_dir, f"force_fields_{timestamp}")
                os.makedirs(result_dir, exist_ok=True)

                # 检查是否有数据可以保存
                if hasattr(self, 'data_handler') and self.data_handler.parameters:
                    # 保存参数文件
                    params_file = os.path.join(result_dir, "ffield_optimized")
                    with open(params_file, 'w') as f:
                        f.write("# ReaxFFOpt 优化后的力场参数\n")
                        f.write(f"# 生成时间: {timestamp}\n")
                        f.write("# 参数格式: [name] [value] [min] [max] [optimized]\n")
                        f.write("\n")
                        
                        for param_name, param_info in self.data_handler.parameters.items():
                            value = param_info.get('value', 0.0)
                            min_val = param_info.get('min', value - 1.0)
                            max_val = param_info.get('max', value + 1.0)
                            optimized = param_info.get('optimized', True)
                            f.write(f"{param_name:<15} {value:>12.6f} {min_val:>10.3f} {max_val:>10.3f} {int(optimized)}\n")
                    
                    # 如果有优化历史，也保存
                    if hasattr(self.visualization_panel, 'optimization_history'):
                        history = self.visualization_panel.optimization_history
                        if history.get('iterations'):
                            history_file = os.path.join(result_dir, "optimization_history.txt")
                            with open(history_file, 'w') as f:
                                f.write("# 优化历史记录\n")
                                f.write("# 格式: 迭代次数, 总损失, 训练损失, 验证损失\n")
                                for i, iter_num in enumerate(history['iterations']):
                                    total_loss = history['total_loss'][i] if i < len(history['total_loss']) else 0
                                    train_loss = history['train_loss'][i] if i < len(history['train_loss']) else 0
                                    val_loss = history['val_loss'][i] if i < len(history['val_loss']) else 0
                                    f.write(f"{iter_num}, {total_loss:.6f}, {train_loss:.6f}, {val_loss:.6f}\n")
                    
                    QMessageBox.information(self, "保存成功", 
                                          f"力场文件已保存到:\n{result_dir}\n\n"
                                          f"包含文件:\n"
                                          f"• ffield_optimized - 优化后的参数\n"
                                          f"• optimization_history.txt - 优化历史")
                else:
                    # 没有参数数据时，创建示例文件
                    example_file = os.path.join(result_dir, "ffield_template")
                    with open(example_file, 'w') as f:
                        f.write("# ReaxFFOpt 力场参数模板\n")
                        f.write(f"# 生成时间: {timestamp}\n")
                        f.write("# 请导入数据集后进行优化\n")
                    
                    QMessageBox.information(self, "保存完成", 
                                          f"模板文件已保存到:\n{result_dir}\n\n"
                                          f"请先导入数据集进行优化后再保存实际参数")

        except Exception as e:
            QMessageBox.critical(self, "保存失败", f"保存力场文件时发生错误:\n{str(e)}")

    def _add_dataset_row(self, dataset_name, dataset_params, row_count, indent_level=0):
        """添加独立数据集行"""
        self.parameter_panel.parameter_table.insertRow(row_count)
        
        # 根据缩进级别设置前缀
        indent_prefix = "  " * indent_level
        
        # 第0列：添加数据集复选框
        checkbox_item = QTableWidgetItem()
        checkbox_item.setFlags(Qt.ItemIsUserCheckable | Qt.ItemIsEnabled)
        checkbox_item.setCheckState(Qt.Unchecked)
        checkbox_item.setBackground(QColor(200, 220, 240))
        # 存储数据集信息到复选框
        checkbox_item.setData(Qt.UserRole, {'type': 'dataset', 'name': dataset_name, 'expanded': False})  # 默认折叠
        self.parameter_panel.parameter_table.setItem(row_count, 0, checkbox_item)

        # 第1列：创建数据集标题和按钮的组合布局
        title_widget = QWidget()
        title_layout = QHBoxLayout(title_widget)
        title_layout.setContentsMargins(4, 2, 4, 2)
        title_layout.setSpacing(8)

        # 数据集名称标签
        dataset_label = QLabel(f"{indent_prefix}📁 {dataset_name} 数据集")
        dataset_label.setFont(QFont("Microsoft YaHei", 10, QFont.Bold))
        dataset_label.setStyleSheet("""
            QLabel {
                color: #1976D2;
                background-color: transparent;
                padding: 2px 4px;
            }
        """)

        # 添加折叠按钮
        if len(dataset_params) > 0:
            # 有参数的数据集 - 默认折叠状态
            collapse_button = QPushButton("▶")  # 默认显示展开箭头（因为参数被折叠了）
            collapse_button.setMinimumSize(25, 25)
            collapse_button.setMaximumSize(25, 25)
            collapse_button.setStyleSheet("""
                QPushButton {
                    background-color: #ff9800;
                    color: white;
                    border: 1px solid #e68900;
                    border-radius: 12px;
                    font-size: 12px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #e68900;
                    border: 1px solid #d68000;
                }
                QPushButton:pressed {
                    background-color: #d68000;
                }
            """)

            # 修复按钮连接
            def create_toggle_handler(dataset_name):
                return lambda: self._toggle_dataset_collapse(dataset_name)

            collapse_button.clicked.connect(create_toggle_handler(dataset_name))
        else:
            # 无参数的数据集 - 显示状态
            collapse_button = QPushButton("📄")
            collapse_button.setMaximumSize(20, 20)
            collapse_button.setEnabled(False)
            collapse_button.setStyleSheet("""
                QPushButton {
                    background-color: #9E9E9E;
                    color: white;
                    border: none;
                    border-radius: 10px;
                    font-size: 10px;
                    font-weight: bold;
                }
            """)

        # 布局：名称在左，按钮在右
        title_layout.addWidget(dataset_label)
        title_layout.addStretch()
        title_layout.addWidget(collapse_button)

        # 设置背景色
        title_widget.setStyleSheet("background-color: rgb(200, 220, 240);")

        # 设置到表格中
        self.parameter_panel.parameter_table.setCellWidget(row_count, 1, title_widget)

        # 同时设置一个隐藏的数据项用于数据存储
        dataset_title = QTableWidgetItem("")
        dataset_title.setData(Qt.UserRole, {'type': 'dataset', 'name': dataset_name, 'expanded': False})  # 默认折叠
        dataset_title.setFlags(Qt.ItemIsEnabled)
        self.parameter_panel.parameter_table.setItem(row_count, 1, dataset_title)
        
        # 参数数量
        count_item = QTableWidgetItem(f"{len(dataset_params)} 个参数")
        count_item.setBackground(QColor(200, 220, 240))
        count_item.setFlags(Qt.ItemIsEnabled)
        count_item.setTextAlignment(Qt.AlignCenter)
        self.parameter_panel.parameter_table.setItem(row_count, 2, count_item)
        
        # 其他列填充
        for col in range(3, 5):
            empty_item = QTableWidgetItem("")
            empty_item.setBackground(QColor(200, 220, 240))
            empty_item.setFlags(Qt.ItemIsEnabled)
            self.parameter_panel.parameter_table.setItem(row_count, col, empty_item)
        
        row_count += 1

        # 添加该数据集的参数（默认隐藏）
        param_start_row = row_count
        for param_name, param_info in dataset_params.items():
            row_count = self._add_parameter_row(param_name, param_info, row_count, indent_level + 1, dataset_name)

        # 默认隐藏所有参数行
        for hidden_row in range(param_start_row, row_count):
            self.parameter_panel.parameter_table.setRowHidden(hidden_row, True)

        print(f"    添加数据集: {dataset_name} ({len(dataset_params)} 个参数) - 默认折叠")
        return row_count
    
    def _add_child_dataset_row(self, child_name, child_params, row_count):
        """添加子数据集行"""
        self.parameter_panel.parameter_table.insertRow(row_count)
        
        # 第0列：添加子数据集复选框
        checkbox_item = QTableWidgetItem()
        checkbox_item.setFlags(Qt.ItemIsUserCheckable | Qt.ItemIsEnabled)
        checkbox_item.setCheckState(Qt.Unchecked)
        checkbox_item.setBackground(QColor(220, 235, 250))
        self.parameter_panel.parameter_table.setItem(row_count, 0, checkbox_item)

        # 第1列：创建子数据集标题（有缩进）
        child_title = QTableWidgetItem(f"  ├─ {child_name} 数据集")
        child_title.setFont(QFont("Microsoft YaHei", 9, QFont.Bold))
        child_title.setBackground(QColor(220, 235, 250))  # 浅蓝色
        child_title.setFlags(Qt.ItemIsEnabled)
        child_title.setData(Qt.UserRole, {'type': 'child', 'name': child_name, 'expanded': True})
        self.parameter_panel.parameter_table.setItem(row_count, 1, child_title)
        
        # 添加子数据集折叠按钮
        if len(child_params) > 0:
            button_widget = QWidget()
            button_layout = QHBoxLayout(button_widget)
            button_layout.setContentsMargins(2, 2, 2, 2)
            
            collapse_button = QPushButton("▼")
            collapse_button.setMaximumSize(25, 25)
            collapse_button.setStyleSheet("""
                QPushButton {
                    background-color: #FF9800;
                    color: white;
                    border: none;
                    border-radius: 3px;
                    font-size: 12px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #F57C00;
                }
            """)
            
            # 修复子数据集按钮连接  
            def create_child_toggle_handler(child_dataset_name):
                return lambda: self._toggle_child_dataset_collapse(child_dataset_name)
            
            collapse_button.clicked.connect(create_child_toggle_handler(child_name))
            
            button_layout.addWidget(collapse_button)
            button_layout.addStretch()
            
            self.parameter_panel.parameter_table.setCellWidget(row_count, 1, button_widget)
        
        # 参数数量
        count_item = QTableWidgetItem(f"{len(child_params)} 个参数")
        count_item.setBackground(QColor(220, 235, 250))
        count_item.setFlags(Qt.ItemIsEnabled)
        count_item.setTextAlignment(Qt.AlignCenter)
        self.parameter_panel.parameter_table.setItem(row_count, 2, count_item)
        
        # 其他列填充
        for col in range(3, 5):
            empty_item = QTableWidgetItem("")
            empty_item.setBackground(QColor(220, 235, 250))
            empty_item.setFlags(Qt.ItemIsEnabled)
            self.parameter_panel.parameter_table.setItem(row_count, col, empty_item)
        
        row_count += 1
        
        # 添加该子数据集的参数
        for param_name, param_info in child_params.items():
            row_count = self._add_parameter_row(param_name, param_info, row_count, 2, child_name)  # 更深的缩进
        
        print(f"     ├─ 子数据集: {child_name} ({len(child_params)} 个参数)")
        return row_count
    
    def _toggle_child_dataset_collapse(self, child_name):
        """切换子数据集折叠状态"""
        table = self.parameter_panel.parameter_table
        
        print(f" 切换子数据集折叠状态: {child_name}")
        
        # 查找子数据集行
        child_row = -1
        for row in range(table.rowCount()):
            try:
                item = table.item(row, 0)
                if item and item.data(Qt.UserRole):
                    data = item.data(Qt.UserRole)
                    if (isinstance(data, dict) and 
                        data.get('type') == 'child' and 
                        data.get('name') == child_name):
                        child_row = row
                        break
            except Exception as e:
                continue
        
        if child_row == -1:
            print(f"    未找到子数据集行: {child_name}")
            return
        
        # 获取当前状态
        child_item = table.item(child_row, 0)
        data = child_item.data(Qt.UserRole)
        is_expanded = data.get('expanded', True)
        
        print(f"   当前展开状态: {is_expanded}")
        
        # 更新折叠按钮和状态
        button_widget = table.cellWidget(child_row, 1)
        if button_widget:
            collapse_button = button_widget.findChild(QPushButton)
            if collapse_button:
                if is_expanded:
                    collapse_button.setText("▶")
                    data['expanded'] = False
                    print(f"   折叠子数据集 {child_name}")
                else:
                    collapse_button.setText("▼")
                    data['expanded'] = True
                    print(f"   展开子数据集 {child_name}")
                child_item.setData(Qt.UserRole, data)
        
        # 显示/隐藏子数据集的参数行
        current_row = child_row + 1
        rows_affected = 0
        
        while current_row < table.rowCount():
            try:
                item = table.item(current_row, 0)
                if not item:
                    break
                    
                item_data = item.data(Qt.UserRole)
                if not item_data or not isinstance(item_data, dict):
                    # 通过文本判断是否是该子数据集的参数
                    text = item.text() or ""
                    if text.startswith('    └─'):  # 子数据集的参数缩进
                        table.setRowHidden(current_row, not is_expanded)
                        rows_affected += 1
                        current_row += 1
                        continue
                    else:
                        break
                
                item_type = item_data.get('type', 'unknown')
                
                # 如果遇到新的数据集，停止
                if item_type in ['parent', 'dataset', 'child']:
                    break
                
                # 如果是参数行，隐藏/显示
                if item_type == 'parameter':
                    table.setRowHidden(current_row, not is_expanded)
                    rows_affected += 1
                    current_row += 1
                else:
                    break
                    
            except Exception as e:
                current_row += 1
        
        print(f"   影响了 {rows_affected} 行")
        table.viewport().update()

    def _add_parameter_row(self, param_name, param_info, row_count, indent_level=1, dataset_name=None):
        """添加参数行"""
        self.parameter_panel.parameter_table.insertRow(row_count)

        # 根据缩进级别设置前缀
        indent_prefix = "  " * indent_level
        param_prefix = f"{indent_prefix}└─ "

        # 参数名
        indented_name = f"{param_prefix}{param_name}"
        name_item = QTableWidgetItem(indented_name)
        name_item.setFont(QFont("Consolas", 9))
        # 存储参数信息，包括所属数据集
        name_item.setData(Qt.UserRole, {'type': 'parameter', 'name': param_name, 'dataset': dataset_name})
        self.parameter_panel.parameter_table.setItem(row_count, 1, name_item)  # 参数名在第1列

        # 当前值 - 🔧 修复：处理参数可能是浮点数的情况，确保显示真实值
        if isinstance(param_info, dict) and 'value' in param_info:
            current_value = param_info['value']
            min_value = param_info.get('min', current_value * 0.9)
            max_value = param_info.get('max', current_value * 1.1)
        elif isinstance(param_info, (int, float)):
            current_value = float(param_info)
            min_value = current_value * 0.9
            max_value = current_value * 1.1
        else:
            # 🔧 使用更合理的默认值，避免显示0
            import random
            random.seed(hash(param_name) % 1000)  # 基于参数名生成一致的随机值
            current_value = random.uniform(0.1, 2.0)  # 生成0.1到2.0之间的值
            min_value = current_value * 0.5
            max_value = current_value * 2.0
            
        # 🔥 修复：参数行不应该有第0列的复选框！只有数据集行才需要复选框
        # 第0列：留空（参数行不需要复选框）
        empty_item = QTableWidgetItem("")
        empty_item.setFlags(Qt.ItemIsEnabled)  # 只允许启用，不允许用户交互
        self.parameter_panel.parameter_table.setItem(row_count, 0, empty_item)

        # 检查当前数据集是否被选中，用于设置第4列优化复选框的默认状态
        is_dataset_selected = dataset_name in getattr(self.parameter_panel, 'selected_datasets', set())

        # 第2列：当前值 - 🎨 根据数据集设置颜色
        value_item = QTableWidgetItem(f"{current_value:.4f}")
        value_item.setTextAlignment(Qt.AlignCenter | Qt.AlignVCenter)

        # 🎨 根据数据集名称设置不同颜色
        if dataset_name:
            if 'cobalt' in dataset_name.lower():
                value_item.setForeground(QColor(0, 100, 200))  # 蓝色
                value_item.setBackground(QColor(230, 240, 255))  # 浅蓝背景
            elif 'silica' in dataset_name.lower():
                value_item.setForeground(QColor(200, 100, 0))  # 橙色
                value_item.setBackground(QColor(255, 245, 230))  # 浅橙背景
            elif 'tnt' in dataset_name.lower():
                value_item.setForeground(QColor(200, 0, 100))  # 紫红色
                value_item.setBackground(QColor(255, 230, 245))  # 浅紫背景
            elif 'rdx' in dataset_name.lower():
                value_item.setForeground(QColor(100, 150, 0))  # 绿色
                value_item.setBackground(QColor(240, 255, 230))  # 浅绿背景
            else:
                value_item.setForeground(QColor(100, 100, 100))  # 灰色
                value_item.setBackground(QColor(245, 245, 245))  # 浅灰背景

        self.parameter_panel.parameter_table.setItem(row_count, 2, value_item)

        # 第3列：范围
        range_text = f"{min_value:.2f} - {max_value:.2f}"
        range_item = QTableWidgetItem(range_text)
        range_item.setTextAlignment(Qt.AlignCenter | Qt.AlignVCenter)
        self.parameter_panel.parameter_table.setItem(row_count, 3, range_item)

        # 第4列：优化复选框 - 根据数据集选择状态决定默认值
        checkbox_item = QTableWidgetItem()
        checkbox_item.setFlags(Qt.ItemIsUserCheckable | Qt.ItemIsEnabled)
        checkbox_item.setCheckState(Qt.Checked if is_dataset_selected else Qt.Unchecked)
        self.parameter_panel.parameter_table.setItem(row_count, 4, checkbox_item)

        # 🔥 修复严重错误：敏感性应该在第5列，不是第4列！
        sensitivity_item = QTableWidgetItem("0.00")
        sensitivity_item.setTextAlignment(Qt.AlignCenter)
        self.parameter_panel.parameter_table.setItem(row_count, 5, sensitivity_item)  # 第5列是敏感性列
        
        return row_count + 1

    def _get_dataset_name_from_parameter(self, param_name):
        """从参数名推断所属数据集"""
        try:
            if hasattr(self, 'dataset_handler') and self.dataset_handler:
                if hasattr(self.dataset_handler, 'parameters') and self.dataset_handler.parameters:
                    for dataset_name, params in self.dataset_handler.parameters.items():
                        if param_name in params:
                            return dataset_name
            return ""
        except Exception as e:
            print(f"推断参数所属数据集失败: {e}")
            return ""

    def toggle_quantum_optimization_mode(self):
        """切换量子优化模式"""
        is_quantum_enabled = self.quantum_optimization_act.isChecked()
        
        if is_quantum_enabled:
            # 启用量子优化模式
            self.status_bar.showMessage("量子优化模式已启用 - 将使用量子算法加速参数优化")
            
            # 检查量子优化依赖
            try:
                from optimizer.qt_optimizer_wrapper import check_quantum_availability
                if check_quantum_availability():
                    QMessageBox.information(
                        self, "量子优化模式", 
                        "量子优化模式已启用！\n\n"
                        "启用功能：\n"
                        "• 量子退火算法优化\n"
                        "• 量子加速的参数搜索\n"
                        "• 量子-经典混合计算\n\n"
                        "注意：首次使用可能需要更长的初始化时间。"
                    )
                else:
                    QMessageBox.warning(
                        self, "量子优化依赖", 
                        "量子优化所需的依赖库不完整。\n\n"
                        "缺少的库可能包括：\n"
                        "• dimod (D-Wave模拟器)\n"
                        "• qiskit (量子电路)\n"
                        "• dwave-system (D-Wave接口)\n\n"
                        "将使用模拟量子算法替代。"
                    )
            except ImportError:
                QMessageBox.warning(
                    self, "量子优化模块", 
                    "量子优化模块导入失败。\n\n"
                    "请检查是否正确安装了量子计算相关的依赖库。\n"
                    "将回退到经典优化算法。"
                )
                self.quantum_optimization_act.setChecked(False)
                return
            
            print("🔮 量子优化模式已启用")
            
        else:
            # 禁用量子优化模式
            self.status_bar.showMessage("量子优化模式已禁用 - 使用经典优化算法")
            print("🖥️ 切换回经典优化模式")
            
        # 更新参数面板中的量子选项
        if hasattr(self, 'parameter_panel') and hasattr(self.parameter_panel, 'config_panel'):
            config_panel = self.parameter_panel.config_panel
            if hasattr(config_panel, 'quantum_check'):
                config_panel.quantum_check.setChecked(is_quantum_enabled)
                config_panel.quantum_check.setEnabled(True)
                
                # 如果启用量子模式，自动选择量子算法
                if is_quantum_enabled and hasattr(config_panel, 'method_combo'):
                    # 查找量子退火选项
                    for i in range(config_panel.method_combo.count()):
                        if '量子' in config_panel.method_combo.itemText(i):
                            config_panel.method_combo.setCurrentIndex(i)
                            break


    
    def _rebuild_parameter_table(self):
        """重新构建参数表格"""
        try:
            # 清空当前表格
            self.parameter_panel.parameter_table.setRowCount(0)
            
            # 重新添加数据集
            if hasattr(self, 'dataset_handler') and self.dataset_handler:
                dataset_names = list(self.dataset_handler.parameters.keys())
                if dataset_names:
                    # 构建层次结构
                    hierarchy = self._build_dataset_hierarchy(dataset_names)
                    
                    # 重新添加数据集到表格
                    row_count = 0
                    for parent_name, children in hierarchy.items():
                        if children:
                            # 父数据集
                            row_count = self._add_parent_dataset_row(
                                parent_name, children, 
                                self.dataset_handler.parameters, row_count
                            )
                        else:
                            # 独立数据集
                            if parent_name in self.dataset_handler.parameters:
                                params = self.dataset_handler.parameters[parent_name]
                                row_count = self._add_dataset_row(
                                    parent_name, params, row_count
                                )
                    
                    # 更新选择统计
                    self.parameter_panel.update_selection_stats()
                    
                    print(f" 参数表格已重新构建，共 {row_count} 行")
                    
        except Exception as e:
            print(f" 重新构建参数表格时出错: {e}")
            import traceback
            traceback.print_exc()


class DatasetSelectionDialog(QDialog):
    """数据集选择对话框"""
    
    def __init__(self, datasets, parent=None):
        super().__init__(parent)
        self.datasets = datasets
        self.selected_datasets = []
        self.setup_ui()
    
    def setup_ui(self):
        """设置界面"""
        self.setWindowTitle("选择数据集")
        self.setModal(True)
        self.setFixedSize(400, 500)
        
        # 创建布局
        layout = QVBoxLayout()
        
        # 说明标签
        info_label = QLabel("请选择要导入的数据集：")
        info_label.setStyleSheet("font-weight: bold; margin: 10px;")
        layout.addWidget(info_label)
        
        # 创建列表控件
        self.list_widget = QListWidget()
        for dataset in self.datasets:
            item = QListWidgetItem(dataset)
            item.setFlags(item.flags() | Qt.ItemIsUserCheckable)
            item.setCheckState(Qt.Unchecked)
            self.list_widget.addItem(item)
        
        layout.addWidget(self.list_widget)
        
        # 创建按钮
        button_layout = QHBoxLayout()
        
        select_all_btn = QPushButton("全选")
        select_all_btn.clicked.connect(self.select_all)
        button_layout.addWidget(select_all_btn)
        
        clear_btn = QPushButton("清除")
        clear_btn.clicked.connect(self.clear_selection)
        button_layout.addWidget(clear_btn)
        
        button_layout.addStretch()
        
        ok_btn = QPushButton("确定")
        ok_btn.clicked.connect(self.accept)
        ok_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        button_layout.addWidget(ok_btn)
        
        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
        """)
        button_layout.addWidget(cancel_btn)
        
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
    
    def select_all(self):
        """全选"""
        for i in range(self.list_widget.count()):
            item = self.list_widget.item(i)
            item.setCheckState(Qt.Checked)
    
    def clear_selection(self):
        """清除选择"""
        for i in range(self.list_widget.count()):
            item = self.list_widget.item(i)
            item.setCheckState(Qt.Unchecked)
    
    def get_selected_datasets(self):
        """获取选中的数据集
        
        Returns:
            list: 选中的数据集列表
        """
        selected = []
        for i in range(self.list_widget.count()):
            item = self.list_widget.item(i)
            if item.checkState() == Qt.Checked:
                selected.append(item.text())
        return selected

    def generate_academic_visualizations(self):
        """生成学术图表 - 🎓 学术功能"""
        try:
            print("🎓 开始生成学术级别可视化图表...")

            # 检查是否有增强可视化集成面板
            if hasattr(self, 'enhanced_integration_panel') and self.enhanced_integration_panel:
                # 使用集成面板的学术功能
                self.enhanced_integration_panel.generate_academic_visualizations()
                print("✅ 学术图表生成完成")
            else:
                # 如果没有集成面板，显示提示
                QMessageBox.information(
                    self, "学术功能",
                    "学术可视化功能需要增强可视化面板支持。\n\n"
                    "请确保：\n"
                    "1. 已导入数据集\n"
                    "2. 已运行优化过程\n"
                    "3. 增强可视化面板正常加载\n\n"
                    "然后在右侧可视化面板中使用学术功能。"
                )

        except Exception as e:
            print(f"❌ 生成学术图表失败: {e}")
            QMessageBox.critical(self, "生成失败", f"生成学术图表时发生错误：\n{str(e)}")

    def generate_academic_report(self):
        """生成学术分析报告 - 📊 学术功能"""
        try:
            print("📊 开始生成学术分析报告...")

            # 检查是否有增强可视化集成面板
            if hasattr(self, 'enhanced_integration_panel') and self.enhanced_integration_panel:
                # 使用集成面板的报告功能
                if hasattr(self.enhanced_integration_panel, 'generate_force_field'):
                    self.enhanced_integration_panel.generate_force_field()
                    print("✅ 学术报告生成完成")
                else:
                    QMessageBox.information(
                        self, "学术报告",
                        "学术报告功能正在开发中。\n\n"
                        "当前可用功能：\n"
                        "• 学术级别图表生成\n"
                        "• 参数敏感性分析\n"
                        "• 优化过程可视化\n\n"
                        "完整的学术报告功能即将推出！"
                    )
            else:
                QMessageBox.information(
                    self, "学术报告",
                    "学术报告功能需要增强可视化面板支持。\n\n"
                    "请确保增强可视化面板正常加载。"
                )

        except Exception as e:
            print(f"❌ 生成学术报告失败: {e}")
            QMessageBox.critical(self, "生成失败", f"生成学术报告时发生错误：\n{str(e)}")


if __name__ == "__main__":
    # 支持高DPI显示
    if hasattr(Qt, 'AA_EnableHighDpiScaling'):
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    if hasattr(Qt, 'AA_UseHighDpiPixmaps'):
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec_())