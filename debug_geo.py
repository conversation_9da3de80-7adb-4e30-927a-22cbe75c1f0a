#!/usr/bin/env python
"""
调试geo文件解析问题
"""

import sys
import os
import logging

# 设置详细的日志
logging.basicConfig(level=logging.DEBUG, format='%(levelname)s: %(message)s')

def debug_geo_parsing():
    """调试geo文件解析"""
    print("=== 调试geo文件解析 ===")
    
    try:
        from data.data_handler import DatasetHandler
        
        # 创建处理器
        handler = DatasetHandler()
        
        # 测试解析cobalt数据集的geo文件
        geo_file = 'Datasets/cobalt/geo'
        
        if not os.path.exists(geo_file):
            print(f"❌ geo文件不存在: {geo_file}")
            return False
            
        print(f"解析geo文件: {geo_file}")
        print(f"文件大小: {os.path.getsize(geo_file)} 字节")
        
        # 先读取文件的前几行看看格式
        print("\n前20行内容:")
        with open(geo_file, 'r', encoding='utf-8') as f:
            for i, line in enumerate(f):
                if i >= 20:
                    break
                print(f"{i+1:3d}: {line.rstrip()}")
        
        print("\n开始解析...")
        
        # 直接调用geo解析方法
        structures = handler._parse_geo(geo_file)
        
        print(f"\n解析结果: 找到 {len(structures)} 个结构")
        
        if structures:
            print("\n结构详情:")
            for i, struct in enumerate(structures[:3]):  # 只显示前3个
                print(f"  结构 {i+1}:")
                print(f"    描述: '{struct['description']}'")
                print(f"    原子数: {len(struct['atoms'])}")
                print(f"    坐标数组形状: {struct['coordinates'].shape if hasattr(struct['coordinates'], 'shape') else 'N/A'}")
                if struct['energy'] is not None:
                    print(f"    能量: {struct['energy']}")
                if struct['cell'] is not None:
                    print(f"    晶胞: {struct['cell']}")
                if struct['atoms']:
                    print(f"    前3个原子:")
                    for j, atom in enumerate(struct['atoms'][:3]):
                        print(f"      {j+1}: {atom['symbol']} at ({atom['x']:.3f}, {atom['y']:.3f}, {atom['z']:.3f})")
                print()
        else:
            print("❌ 没有解析到任何结构")
            
            # 尝试手动解析几行看看问题在哪
            print("\n手动解析测试:")
            with open(geo_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            current_structure = None
            for i, line in enumerate(lines[:50]):  # 只看前50行
                line = line.strip()
                if not line:
                    continue
                    
                print(f"行 {i+1}: {line}")
                
                if line.startswith("XTLGRF") or line.startswith("BIOGRF"):
                    print("  -> 发现结构开始标记")
                    current_structure = {
                        'description': '',
                        'atoms': [],
                        'coordinates': [],
                        'atom_types': [],
                        'energy': None,
                        'cell': None
                    }
                elif line.startswith("DESCRP"):
                    if current_structure is not None:
                        current_structure['description'] = line[7:].strip()
                        print(f"  -> 设置描述: '{current_structure['description']}'")
                elif line.startswith("HETATM"):
                    if current_structure is not None:
                        parts = line.split()
                        print(f"  -> HETATM行分割结果: {parts}")
                        if len(parts) >= 8:
                            try:
                                atom_id = int(parts[1])
                                atom_symbol = parts[2]
                                x = float(parts[3])
                                y = float(parts[4])
                                z = float(parts[5])
                                print(f"  -> 解析原子: {atom_symbol} at ({x}, {y}, {z})")
                            except Exception as e:
                                print(f"  -> 解析原子失败: {e}")
                elif line.startswith("ENERGY"):
                    if current_structure is not None:
                        parts = line.split()
                        if len(parts) >= 2:
                            try:
                                energy = float(parts[1])
                                current_structure['energy'] = energy
                                print(f"  -> 设置能量: {energy}")
                            except Exception as e:
                                print(f"  -> 解析能量失败: {e}")
                elif line.startswith("END"):
                    if current_structure and len(current_structure['atoms']) > 0:
                        print(f"  -> 结构结束，原子数: {len(current_structure['atoms'])}")
                    else:
                        print("  -> 结构结束，但没有原子")
                    break
            
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ geo解析测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("开始geo文件解析调试...")
    print(f"当前工作目录: {os.getcwd()}")
    
    success = debug_geo_parsing()
    
    if success:
        print("🎉 geo文件解析成功！")
    else:
        print("❌ geo文件解析失败，请检查上述错误信息")

if __name__ == "__main__":
    main()
