"""
ReaxFFOpt 数据处理模块
负责导入和解析各种类型的数据集文件
"""

import os
import numpy as np
import glob
from PyQt5.QtCore import QThread, pyqtSignal
import logging
import time

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatasetWorker(QThread):
    """数据集处理工作线程类"""
    progress = pyqtSignal(int)
    finished = pyqtSignal(dict)

    def __init__(self, folder_path, selected_datasets=None):
        super().__init__()
        self.folder_path = folder_path
        self.selected_datasets = selected_datasets
        self.handler = DatasetHandler()

    def run(self):
        """处理数据集文件夹"""
        logger.info(f"开始处理数据集文件夹: {self.folder_path}")

        try:
            # 如果没有指定数据集，则处理所有数据集
            if not self.selected_datasets:
                result = self.handler.process_folder(self.folder_path, self.progress.emit)
            else:
                # 只处理选中的数据集
                total_progress = len(self.selected_datasets)
                processed = 0

                for dataset in self.selected_datasets:
                    dataset_path = os.path.join(self.folder_path, dataset)
                    if os.path.exists(dataset_path) and os.path.isdir(dataset_path):
                        # 处理单个数据集
                        self.handler.process_folder(dataset_path)

                        # 更新进度
                        processed += 1
                        progress = int((processed / total_progress) * 100)
                        self.progress.emit(progress)

                # 收集处理结果
                result = {
                    "structures": len(self.handler.structures),
                    "energies": len(self.handler.energies),
                    "forces": len(self.handler.forces),
                    "training_sets": len(self.handler.training_sets),
                    "parameters": len(self.handler.parameters)
                }

            logger.info(f"数据集处理完成: {result}")
            self.finished.emit(result)

        except Exception as e:
            logger.error(f"处理数据集时发生错误: {str(e)}", exc_info=True)
            self.finished.emit({})  # 发送空结果表示处理失败

class DatasetHandler:
    """数据集处理类"""

    def __init__(self):
        # 存储不同类型的数据
        self.structures = {}  # 分子结构
        self.energies = {}    # 能量数据
        self.forces = {}      # 力数据
        self.training_sets = {}  # 训练集定义
        self.parameters = {}  # 力场参数
        self.calculation_results = {}  # 计算结果存储

    def clear_data(self):
        """清除所有数据"""
        self.structures.clear()
        self.energies.clear()
        self.forces.clear()
        self.training_sets.clear()
        self.parameters.clear()
        self.calculation_results.clear()
        logger.info("已清除所有数据")

    def process_folder(self, folder_path, progress_callback=None):
        """处理整个数据集文件夹"""
        logger.info(f"开始处理数据集文件夹: {folder_path}")

        # 清除之前的数据
        self.clear_data()

        # 获取所有文件和子目录
        all_files = []
        for root, dirs, files in os.walk(folder_path):
            for file in files:
                all_files.append(os.path.join(root, file))

        total_files = len(all_files)
        processed_files = 0

        # 首先处理参数文件
        for file_path in all_files:
            if os.path.basename(file_path).lower() == 'params':
                try:
                    params = self._parse_params(file_path)
                    if params:
                        dataset_name = os.path.basename(os.path.dirname(file_path))
                        self.parameters[dataset_name] = params
                        logger.info(f"成功加载参数文件: {file_path}")
                except Exception as e:
                    logger.error(f"处理参数文件失败: {file_path}, 错误: {str(e)}")

                processed_files += 1
                if progress_callback:
                    progress = int((processed_files / total_files) * 100)
                    progress_callback(progress)

        # 然后处理geo文件
        for file_path in all_files:
            basename = os.path.basename(file_path).lower()
            if basename == 'geo' or basename.endswith('.geo'):
                try:
                    structures = self._parse_geo(file_path)
                    if structures:
                        dataset_name = os.path.basename(os.path.dirname(file_path))
                        self.structures[dataset_name] = structures
                        logger.info(f"成功加载结构文件: {file_path}")
                except Exception as e:
                    logger.error(f"处理结构文件失败: {file_path}, 错误: {str(e)}")

                processed_files += 1
                if progress_callback:
                    progress = int((processed_files / total_files) * 100)
                    progress_callback(progress)

        # 最后处理训练集文件
        for file_path in all_files:
            if os.path.basename(file_path).lower() == 'trainset.in':
                try:
                    training_set = self._parse_trainset(file_path)
                    if training_set:
                        dataset_name = os.path.basename(os.path.dirname(file_path))
                        self.training_sets[dataset_name] = training_set
                        logger.info(f"成功加载训练集文件: {file_path}")
                except Exception as e:
                    logger.error(f"处理训练集文件失败: {file_path}, 错误: {str(e)}")

                processed_files += 1
                if progress_callback:
                    progress = int((processed_files / total_files) * 100)
                    progress_callback(progress)

        # 收集处理结果
        result = {
            "structures": len(self.structures),
            "energies": len(self.energies),
            "forces": len(self.forces),
            "training_sets": len(self.training_sets),
            "parameters": len(self.parameters)
        }

        logger.info(f"数据集处理完成，结果: {result}")
        return result

    def _identify_file_type(self, file_path):
        """识别文件类型"""
        filename = os.path.basename(file_path).lower()

        # 基于文件名识别
        if filename == "geo":
            return "geo"
        elif filename == "trainset.in":
            return "trainset"
        elif filename.startswith("ffield"):
            return "ffield"
        elif filename == "params":
            return "params"

        # 如果文件名无法识别，尝试通过内容识别
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                first_line = f.readline().strip()
                if "BIOGRF" in first_line:
                    return "geo"
                elif "ATOMS" in first_line:
                    return "trainset"
                elif "Reactive MD-force field" in first_line:
                    return "ffield"
        except:
            pass

        return "unknown"

    def _parse_geo(self, file_path):
        """解析ReaxFF几何文件(geo)"""
        structures = []
        current_structure = None

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # 处理XTLGRF和BIOGRF格式的开始标记
                if line.startswith("XTLGRF") or line.startswith("BIOGRF"):
                    if current_structure and len(current_structure['atoms']) > 0:
                        # 将坐标和原子类型转换为numpy数组
                        current_structure['coordinates'] = np.array(current_structure['coordinates'])
                        current_structure['atom_types'] = np.array(current_structure['atom_types'])
                        structures.append(current_structure)
                    current_structure = {
                        'description': '',
                        'atoms': [],
                        'cell': None,
                        'coordinates': [],  # 添加坐标列表
                        'atom_types': [],   # 添加原子类型列表
                        'energy': None      # 添加能量信息
                    }
                    continue

                if line.startswith("DESCRP"):
                    if current_structure is not None:
                        current_structure['description'] = line[7:].strip()
                    continue
                elif line.startswith("CRYSTX"):
                    # 解析晶胞参数
                    parts = line.split()
                    if len(parts) >= 7:
                        current_structure['cell'] = {
                            'a': float(parts[1]),
                            'b': float(parts[2]),
                            'c': float(parts[3]),
                            'alpha': float(parts[4]),
                            'beta': float(parts[5]),
                            'gamma': float(parts[6])
                        }
                elif line.startswith("HETATM") or line.startswith("ATOM"):
                    try:
                        # 改进的坐标解析逻辑
                        if current_structure is None:
                            continue

                        # 使用固定位置解析（PDB格式标准）
                        if len(line) >= 54:
                            try:
                                # PDB格式固定位置解析
                                atom_type = line[12:16].strip()
                                x = float(line[30:38].strip())
                                y = float(line[38:46].strip())
                                z = float(line[46:54].strip())
                            except (ValueError, IndexError):
                                # 如果固定位置解析失败，尝试空格分割
                                parts = line.split()
                                if len(parts) >= 7:
                                    atom_type = parts[2]
                                    # 寻找坐标部分（通常是连续的三个浮点数）
                                    coord_found = False
                                    for i in range(len(parts) - 2):
                                        try:
                                            x = float(parts[i])
                                            y = float(parts[i + 1])
                                            z = float(parts[i + 2])
                                            coord_found = True
                                            break
                                        except ValueError:
                                            continue
                                    if not coord_found:
                                        continue
                                else:
                                    continue
                        else:
                            # 行太短，尝试空格分割
                            parts = line.split()
                            if len(parts) >= 6:
                                atom_type = parts[2] if len(parts) > 2 else "C"
                                try:
                                    # 寻找坐标（最后三个数字字段）
                                    coords = []
                                    for part in parts:
                                        try:
                                            coords.append(float(part))
                                        except ValueError:
                                            continue
                                    if len(coords) >= 3:
                                        x, y, z = coords[-3:]
                                    else:
                                        continue
                                except:
                                    continue
                            else:
                                continue

                        # 清理原子类型名称
                        atom_type = atom_type.strip()
                        if not atom_type:
                            atom_type = "C"  # 默认为碳原子

                        atom = {
                            'id': len(current_structure['atoms']) + 1,
                            'symbol': atom_type,
                            'x': x,
                            'y': y,
                            'z': z,
                            'charge': 0.0
                        }

                        current_structure['atoms'].append(atom)
                        current_structure['coordinates'].append([x, y, z])
                        current_structure['atom_types'].append(
                            self._convert_symbol_to_type(atom_type)
                        )

                    except Exception as e:
                        # 只在调试模式下显示详细错误
                        if logger.isEnabledFor(logging.DEBUG):
                            logger.debug(f"跳过原子行: {line[:50]}... ({str(e)})")
                        continue

                elif line.startswith("ENERGY"):
                    # 解析能量信息
                    if current_structure is not None:
                        parts = line.split()
                        if len(parts) >= 2:
                            try:
                                energy = float(parts[1])
                                current_structure['energy'] = energy
                                logger.debug(f"找到能量: {energy}")
                            except:
                                pass

                elif line.startswith("END"):
                    # 结构结束
                    if current_structure and len(current_structure['atoms']) > 0:
                        # 将坐标和原子类型转换为numpy数组
                        current_structure['coordinates'] = np.array(current_structure['coordinates'])
                        current_structure['atom_types'] = np.array(current_structure['atom_types'])
                        structures.append(current_structure)
                        current_structure = None

            # 添加最后一个结构（如果文件没有以END结尾）
            if current_structure and len(current_structure['atoms']) > 0:
                # 将坐标和原子类型转换为numpy数组
                current_structure['coordinates'] = np.array(current_structure['coordinates'])
                current_structure['atom_types'] = np.array(current_structure['atom_types'])
                structures.append(current_structure)

            logger.info(f"成功解析geo文件 {file_path}: 找到 {len(structures)} 个结构")

        except Exception as e:
            logger.error(f"解析geo文件失败: {str(e)}", exc_info=True)
            return []

        return structures

    def _convert_symbol_to_type(self, symbol):
        """将原子符号转换为数值类型

        Args:
            symbol (str): 原子符号

        Returns:
            int: 原子类型的数值表示
        """
        # 定义原子符号到数值的映射
        symbol_to_type = {
            'H': 1, 'C': 6, 'N': 7, 'O': 8, 'F': 9,
            'Si': 14, 'P': 15, 'S': 16, 'Cl': 17,
            'Co': 27, 'Ni': 28, 'Cu': 29, 'Zn': 30
        }

        # 移除下标并获取基本符号
        base_symbol = ''.join(c for c in symbol if not c.isdigit())

        # 返回映射的数值，如果没有找到则返回0
        return symbol_to_type.get(base_symbol, 0)

    def _parse_trainset(self, file_path):
        """解析训练集文件(trainset.in)"""
        training_data = {
            'structures': [],
            'energies': [],
            'forces': []
        }

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            current_section = None
            current_data = {}

            for line in lines:
                line = line.strip()
                if not line or line.startswith("#"):
                    continue

                if line.startswith("ENERGY"):
                    if current_data:
                        training_data['energies'].append(current_data)
                    current_section = "energy"
                    current_data = {'type': 'energy'}
                elif line.startswith("FORCE"):
                    if current_data:
                        training_data['forces'].append(current_data)
                    current_section = "force"
                    current_data = {'type': 'force'}
                elif line.startswith("ENDENER") or line.startswith("ENDFOR"):
                    if current_data:
                        if current_section == "energy":
                            training_data['energies'].append(current_data)
                        elif current_section == "force":
                            training_data['forces'].append(current_data)
                    current_section = None
                    current_data = {}
                elif current_section:
                    # 解析数据行
                    parts = line.split()
                    if len(parts) >= 2:
                        try:
                            value = float(parts[0])
                            weight = float(parts[1])
                            current_data['value'] = value
                            current_data['weight'] = weight
                        except:
                            pass

        except Exception as e:
            print(f"解析trainset文件失败: {e}")
            return {'structures': [], 'energies': [], 'forces': []}

        return training_data

    def _parse_params(self, file_path):
        """解析参数文件 - 支持多种格式"""
        params = {}
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            param_count = 0
            for line_num, line in enumerate(lines, 1):
                line = line.strip()
                if not line or line.startswith("#"):
                    continue

                # 移除注释部分
                if '!' in line:
                    line = line.split('!')[0].strip()

                parts = line.split()
                if len(parts) < 3:
                    continue

                try:
                    # 检测格式类型
                    if len(parts) >= 4 and not parts[0].isdigit():
                        # 格式1: param_name current_value min_value max_value
                        param_name = parts[0]
                        current_value = float(parts[1])
                        min_value = float(parts[2])
                        max_value = float(parts[3])
                    elif len(parts) >= 6 and all(p.replace('.', '').replace('-', '').isdigit() or p.replace('.', '').replace('-', '').replace('e', '').replace('E', '').replace('+', '').isdigit() for p in parts[:6]):
                        # 格式2: ReaxFF标准格式 section index1 index2 sensitivity low_value high_value
                        section = int(parts[0])
                        index1 = int(parts[1])
                        index2 = int(parts[2])
                        sensitivity = float(parts[3])
                        low_value = float(parts[4])
                        high_value = float(parts[5])

                        # 创建参数名
                        param_name = f"p_{section}_{index1}_{index2}"
                        current_value = (low_value + high_value) / 2  # 使用中值作为当前值
                        min_value = low_value
                        max_value = high_value
                    else:
                        continue

                    # 确保min <= max
                    if min_value > max_value:
                        min_value, max_value = max_value, min_value

                    params[param_name] = {
                        'value': current_value,
                        'min': min_value,
                        'max': max_value
                    }
                    param_count += 1

                except (ValueError, IndexError) as e:
                    logger.debug(f"跳过第{line_num}行: {line} (错误: {e})")
                    continue

            logger.info(f"成功解析参数文件 {file_path}: 找到 {len(params)} 个参数")

        except Exception as e:
            logger.error(f"解析参数文件失败: {str(e)}", exc_info=True)
            return {}

        return params

    def get_structure(self, name):
        """获取指定名称的结构"""
        return self.structures.get(name)

    def get_training_set(self, name):
        """获取指定名称的训练集"""
        return self.training_sets.get(name)

    def get_all_structures(self):
        """获取所有结构"""
        return self.structures

    def get_all_training_sets(self):
        """获取所有训练集"""
        return self.training_sets

    def get_all_parameters(self):
        """获取所有参数"""
        return self.parameters

    def calculate_energy(self, structure_name):
        """计算指定结构的能量

        Args:
            structure_name (str): 结构名称

        Returns:
            float: 计算得到的能量值
        """
        structure = self.get_structure(structure_name)
        if not structure:
            logger.error(f"未找到结构: {structure_name}")
            return None

        try:
            # 这里添加实际的能量计算逻辑
            # 示例: 使用简单的力场计算
            energy = self._calculate_structure_energy(structure)

            # 存储计算结果
            if structure_name not in self.calculation_results:
                self.calculation_results[structure_name] = {}
            self.calculation_results[structure_name]['energy'] = energy

            return energy

        except Exception as e:
            logger.error(f"计算能量失败: {str(e)}")
            return None

    def calculate_forces(self, structure_name):
        """计算指定结构的原子力

        Args:
            structure_name (str): 结构名称

        Returns:
            numpy.ndarray: 原子力数组
        """
        structure = self.get_structure(structure_name)
        if not structure:
            logger.error(f"未找到结构: {structure_name}")
            return None

        try:
            # 这里添加实际的力计算逻辑
            forces = self._calculate_structure_forces(structure)

            # 存储计算结果
            if structure_name not in self.calculation_results:
                self.calculation_results[structure_name] = {}
            self.calculation_results[structure_name]['forces'] = forces

            return forces

        except Exception as e:
            logger.error(f"计算原子力失败: {str(e)}")
            return None

    def save_ffield(self, file_path, structure_name=None):
        """保存力场文件

        Args:
            file_path (str): 保存路径
            structure_name (str, optional): 指定结构名称，如果不指定则保存所有结构的力场
        """
        try:
            # 收集要保存的力场数据
            ffield_data = {}
            if structure_name:
                if structure_name in self.calculation_results:
                    ffield_data[structure_name] = self.calculation_results[structure_name]
                else:
                    logger.error(f"未找到结构的计算结果: {structure_name}")
                    return False
            else:
                ffield_data = self.calculation_results

            # 保存力场文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write("# Generated ReaxFF force field file\n")
                f.write("# Date: " + time.strftime("%Y-%m-%d %H:%M:%S") + "\n\n")

                for struct_name, results in ffield_data.items():
                    f.write(f"# Structure: {struct_name}\n")
                    if 'energy' in results:
                        f.write(f"Energy: {results['energy']:.6f}\n")
                    if 'forces' in results:
                        f.write("Forces:\n")
                        for force in results['forces']:
                            f.write(f"{force[0]:.6f} {force[1]:.6f} {force[2]:.6f}\n")
                    f.write("\n")

            logger.info(f"成功保存力场文件: {file_path}")
            return True

        except Exception as e:
            logger.error(f"保存力场文件失败: {str(e)}")
            return False

    def _calculate_structure_energy(self, structure):
        """计算结构能量的内部方法"""
        # 这里实现具体的能量计算逻辑
        # 示例: 使用简单的距离求和
        energy = 0.0
        coords = structure['coordinates']
        for i in range(len(coords)):
            for j in range(i + 1, len(coords)):
                dist = np.linalg.norm(coords[i] - coords[j])
                energy += 1.0 / (dist + 1e-6)  # 简单的库仑势能
        return energy

    def _calculate_structure_forces(self, structure):
        """计算结构原子力的内部方法"""
        # 这里实现具体的力计算逻辑
        # 示例: 使用简单的距离导数
        coords = structure['coordinates']
        forces = np.zeros_like(coords)
        for i in range(len(coords)):
            for j in range(len(coords)):
                if i != j:
                    diff = coords[i] - coords[j]
                    dist = np.linalg.norm(diff)
                    force = -diff / (dist + 1e-6)**3  # 简单的库仑力
                    forces[i] += force
        return forces
