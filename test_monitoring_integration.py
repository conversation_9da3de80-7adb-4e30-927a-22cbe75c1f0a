#!/usr/bin/env python3
"""
测试监控集成功能
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_monitoring_import():
    """测试监控工具导入"""
    print("🔍 测试监控工具导入...")
    
    try:
        from monitoring_utils import start_monitoring, update_progress, stop_monitoring
        print("✅ monitoring_utils 导入成功")
        return True
    except ImportError as e:
        print(f"❌ monitoring_utils 导入失败: {e}")
        return False

def test_monitoring_functions():
    """测试监控功能"""
    print("🧪 测试监控功能...")
    
    try:
        from monitoring_utils import start_monitoring, update_progress, stop_monitoring
        
        # 测试启动监控
        print("📊 启动监控测试...")
        start_monitoring("测试数据集", 5)
        
        # 测试进度更新
        print("📈 进度更新测试...")
        for i in range(5):
            update_progress(i, 10.0 - i)
        
        # 测试停止监控
        print("⏹️ 停止监控测试...")
        stop_monitoring()
        
        print("✅ 监控功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 监控功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_parameter_panel_integration():
    """测试参数面板集成"""
    print("🔧 测试参数面板集成...")
    
    try:
        # 模拟参数面板导入
        from gui.parameter_panel import MONITORING_AVAILABLE
        
        print(f"📊 参数面板监控状态: {MONITORING_AVAILABLE}")
        
        if MONITORING_AVAILABLE:
            print("✅ 参数面板监控集成成功")
            return True
        else:
            print("⚠️ 参数面板监控集成失败")
            return False
            
    except Exception as e:
        print(f"❌ 参数面板集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_file_existence():
    """检查必要文件是否存在"""
    print("📁 检查必要文件...")
    
    files_to_check = [
        'monitoring_utils.py',
        'gui/parameter_panel.py',
        'main.py'
    ]
    
    all_exist = True
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"✅ {file_path} 存在")
        else:
            print(f"❌ {file_path} 不存在")
            all_exist = False
    
    return all_exist

def main():
    """主函数"""
    print("🚀 监控集成测试")
    print("=" * 50)
    
    # 检查文件
    print("1. 检查文件存在性")
    files_ok = check_file_existence()
    print()
    
    # 测试导入
    print("2. 测试监控工具导入")
    import_ok = test_monitoring_import()
    print()
    
    # 测试功能
    print("3. 测试监控功能")
    if import_ok:
        function_ok = test_monitoring_functions()
    else:
        function_ok = False
        print("⚠️ 跳过功能测试（导入失败）")
    print()
    
    # 测试集成
    print("4. 测试参数面板集成")
    integration_ok = test_parameter_panel_integration()
    print()
    
    # 总结
    print("=" * 50)
    print("📋 测试结果总结:")
    print(f"   文件检查: {'✅ 通过' if files_ok else '❌ 失败'}")
    print(f"   导入测试: {'✅ 通过' if import_ok else '❌ 失败'}")
    print(f"   功能测试: {'✅ 通过' if function_ok else '❌ 失败'}")
    print(f"   集成测试: {'✅ 通过' if integration_ok else '❌ 失败'}")
    
    if all([files_ok, import_ok, function_ok, integration_ok]):
        print("\n🎉 所有测试通过！监控功能应该正常工作")
        print("\n💡 使用建议:")
        print("   1. 运行 python main.py")
        print("   2. 选择数据集和参数")
        print("   3. 点击'开始优化'按钮")
        print("   4. 查看控制台的监控输出")
        return True
    else:
        print("\n⚠️ 存在问题，监控功能可能无法正常工作")
        print("\n🔧 故障排除:")
        if not files_ok:
            print("   - 确保所有必要文件存在")
        if not import_ok:
            print("   - 检查 monitoring_utils.py 文件")
        if not function_ok:
            print("   - 检查监控函数实现")
        if not integration_ok:
            print("   - 检查参数面板集成代码")
        return False

if __name__ == '__main__':
    try:
        success = main()
        input("\n按回车键退出...")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        input("\n按回车键退出...")
        sys.exit(1)
