#!/usr/bin/env python3
"""
监控仪表板集成模块
将ReaxFF优化器与Web监控仪表板连接
"""

import os
import sys
import threading
import time
import requests
import json
from typing import Optional, Callable, Dict, Any

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class DashboardIntegration:
    """监控仪表板集成类"""
    
    def __init__(self, dashboard_url: str = "http://localhost:5000"):
        self.dashboard_url = dashboard_url
        self.is_connected = False
        self.monitor_thread = None
        self.optimization_callback = None
        
    def start_dashboard_server(self):
        """启动仪表板服务器"""
        try:
            import subprocess
            # 在后台启动服务器
            self.server_process = subprocess.Popen([
                sys.executable, 'dashboard_server.py'
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            # 等待服务器启动
            time.sleep(3)
            
            # 检查服务器是否启动成功
            if self.check_connection():
                print("✅ 监控仪表板服务器已启动")
                return True
            else:
                print("❌ 监控仪表板服务器启动失败")
                return False
                
        except Exception as e:
            print(f"❌ 启动监控仪表板失败: {e}")
            return False
    
    def check_connection(self) -> bool:
        """检查与仪表板的连接"""
        try:
            response = requests.get(f"{self.dashboard_url}/api/status", timeout=5)
            self.is_connected = response.status_code == 200
            return self.is_connected
        except:
            self.is_connected = False
            return False
    
    def send_optimization_data(self, iteration: int, loss: float, 
                             gradient_norm: Optional[float] = None,
                             parameters: Optional[Dict] = None):
        """发送优化数据到仪表板"""
        if not self.is_connected:
            return
            
        try:
            data = {
                'iteration': iteration,
                'loss': loss,
                'gradient_norm': gradient_norm,
                'parameters': parameters
            }
            
            # 这里应该通过Socket.IO发送，但为了简化，我们使用HTTP
            # 在实际的dashboard_server.py中，数据会通过Socket.IO实时推送
            
        except Exception as e:
            print(f"⚠️ 发送监控数据失败: {e}")
    
    def create_optimizer_callback(self):
        """创建优化器回调函数"""
        def callback(iteration: int, loss: float, gradient_norm: Optional[float] = None, 
                    parameters: Optional[Dict] = None, **kwargs):
            """优化器回调函数"""
            self.send_optimization_data(iteration, loss, gradient_norm, parameters)
            
            # 打印进度信息
            if iteration % 10 == 0:  # 每10次迭代打印一次
                print(f"📊 迭代 {iteration}: 损失 = {loss:.6f}")
                if gradient_norm is not None:
                    print(f"   梯度范数 = {gradient_norm:.6f}")
        
        return callback
    
    def integrate_with_parameter_panel(self, parameter_panel):
        """与参数面板集成"""
        try:
            # 获取原始的优化启动方法
            original_start_optimization = parameter_panel.start_optimization
            
            def enhanced_start_optimization():
                """增强的优化启动方法"""
                print("🚀 启动优化，同时启用监控仪表板...")
                
                # 启动仪表板服务器
                if not self.is_connected:
                    self.start_dashboard_server()
                
                # 创建监控回调
                callback = self.create_optimizer_callback()
                
                # 如果优化器支持回调，添加监控回调
                # 这里需要根据实际的优化器接口进行调整
                
                # 调用原始的优化方法
                return original_start_optimization()
            
            # 替换优化启动方法
            parameter_panel.start_optimization = enhanced_start_optimization
            
            print("✅ 监控仪表板已集成到参数面板")
            
        except Exception as e:
            print(f"❌ 集成监控仪表板失败: {e}")

def add_monitoring_to_optimizer(optimizer_class):
    """装饰器：为优化器类添加监控功能"""
    
    class MonitoredOptimizer(optimizer_class):
        def __init__(self, *args, **kwargs):
            super().__init__(*args, **kwargs)
            self.dashboard = DashboardIntegration()
            self.monitoring_enabled = False
        
        def enable_monitoring(self, auto_start_dashboard=True):
            """启用监控"""
            self.monitoring_enabled = True
            if auto_start_dashboard:
                self.dashboard.start_dashboard_server()
        
        def optimize(self, *args, **kwargs):
            """重写优化方法，添加监控"""
            if self.monitoring_enabled:
                print("📊 监控已启用，数据将发送到仪表板")
            
            # 调用原始优化方法
            return super().optimize(*args, **kwargs)
        
        def _update_progress(self, iteration, loss, gradient_norm=None, **kwargs):
            """更新进度（需要在优化循环中调用）"""
            if self.monitoring_enabled:
                self.dashboard.send_optimization_data(iteration, loss, gradient_norm)
            
            # 如果父类有进度更新方法，也调用它
            if hasattr(super(), '_update_progress'):
                super()._update_progress(iteration, loss, gradient_norm, **kwargs)
    
    return MonitoredOptimizer

# 使用示例
def demo_integration():
    """演示集成功能"""
    print("🔧 ReaxFF监控仪表板集成演示")
    print("=" * 50)
    
    # 创建集成实例
    integration = DashboardIntegration()
    
    # 启动仪表板服务器
    if integration.start_dashboard_server():
        print("✅ 仪表板服务器已启动")
        print("🌐 访问地址: http://localhost:5000")
        
        # 模拟优化过程
        callback = integration.create_optimizer_callback()
        
        print("🔄 开始模拟优化过程...")
        import numpy as np
        
        for i in range(50):
            # 模拟损失函数下降
            loss = 10.0 * np.exp(-i/20) + np.random.normal(0, 0.1)
            gradient_norm = 5.0 * np.exp(-i/10) + abs(np.random.normal(0, 0.05))
            
            callback(i, loss, gradient_norm)
            time.sleep(0.5)  # 每0.5秒更新一次
        
        print("✅ 模拟优化完成")
    else:
        print("❌ 仪表板服务器启动失败")

if __name__ == '__main__':
    demo_integration()
