#!/usr/bin/env python3
"""
ReaxFF优化监控仪表板服务器
基于Flask + Socket.IO的实时监控系统
"""

import os
import sys
import time
import json
import threading
from datetime import datetime, timedelta
from flask import Flask, render_template, request, jsonify

# 尝试导入可选依赖
try:
    from flask_socketio import SocketIO, emit
    SOCKETIO_AVAILABLE = True
except ImportError:
    print("⚠️ Flask-SocketIO不可用，使用HTTP轮询模式")
    SOCKETIO_AVAILABLE = False

try:
    import plotly.graph_objs as go
    import plotly.utils
    PLOTLY_AVAILABLE = True
except ImportError:
    print("⚠️ Plotly不可用，使用简化图表")
    PLOTLY_AVAILABLE = False

import numpy as np

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class OptimizationMonitor:
    """优化过程监控器"""
    
    def __init__(self):
        self.is_running = False
        self.is_paused = False
        self.start_time = None
        self.current_iteration = 0
        self.current_loss = float('inf')
        self.best_loss = float('inf')
        self.loss_history = []
        self.gradient_history = []
        self.parameter_history = []
        self.runtime_history = []
        
    def start_optimization(self):
        """开始优化"""
        self.is_running = True
        self.is_paused = False
        self.start_time = datetime.now()
        self.current_iteration = 0
        self.loss_history = []
        self.gradient_history = []
        self.parameter_history = []
        self.runtime_history = []
        
    def pause_optimization(self):
        """暂停优化"""
        self.is_paused = True
        
    def resume_optimization(self):
        """恢复优化"""
        self.is_paused = False
        
    def stop_optimization(self):
        """停止优化"""
        self.is_running = False
        self.is_paused = False
        
    def update_progress(self, iteration, loss, gradient_norm=None, parameters=None):
        """更新优化进度"""
        if not self.is_running or self.is_paused:
            return
            
        self.current_iteration = iteration
        self.current_loss = loss
        
        if loss < self.best_loss:
            self.best_loss = loss
            
        # 记录历史数据
        self.loss_history.append(loss)
        if gradient_norm is not None:
            self.gradient_history.append(gradient_norm)
        if parameters is not None:
            self.parameter_history.append(parameters)
            
        # 计算运行时间
        if self.start_time:
            runtime = datetime.now() - self.start_time
            self.runtime_history.append(runtime.total_seconds())
            
    def get_runtime_str(self):
        """获取运行时间字符串"""
        if not self.start_time:
            return "00:00:00"
        runtime = datetime.now() - self.start_time
        hours, remainder = divmod(int(runtime.total_seconds()), 3600)
        minutes, seconds = divmod(remainder, 60)
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        
    def get_current_data(self):
        """获取当前数据"""
        return {
            'iteration': self.current_iteration,
            'loss': self.current_loss,
            'best_loss': self.best_loss,
            'runtime': self.get_runtime_str(),
            'is_running': self.is_running,
            'is_paused': self.is_paused
        }

# 创建Flask应用
app = Flask(__name__, template_folder='templates')
app.config['SECRET_KEY'] = 'reaxff_dashboard_secret'

# 根据可用性创建SocketIO
if SOCKETIO_AVAILABLE:
    socketio = SocketIO(app, cors_allowed_origins="*", async_mode='threading')
else:
    socketio = None

# 全局监控器实例
monitor = OptimizationMonitor()

@app.route('/')
def dashboard():
    """主仪表板页面"""
    return render_template('dashboard.html')

@app.route('/api/control', methods=['POST'])
def control_optimization():
    """控制优化过程"""
    try:
        data = request.get_json()
        action = data.get('action')
        
        if action == 'pause':
            monitor.pause_optimization()
            return jsonify({'status': 'paused'})
        elif action == 'resume':
            monitor.resume_optimization()
            return jsonify({'status': 'resumed'})
        elif action == 'stop':
            monitor.stop_optimization()
            return jsonify({'status': 'stopped'})
        else:
            return jsonify({'error': 'Invalid action'}), 400
            
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# SocketIO事件处理（仅在可用时）
if SOCKETIO_AVAILABLE:
    @socketio.on('connect')
    def handle_connect():
        """客户端连接"""
        print('客户端已连接到监控仪表板')
        # 发送当前数据
        emit('update_data', monitor.get_current_data())

    @socketio.on('disconnect')
    def handle_disconnect():
        """客户端断开连接"""
        print('客户端已断开连接')

    @socketio.on('request_plot')
    def handle_plot_request(data):
        """处理图表请求"""
        plot_type = data.get('type', 'loss')

        try:
            if PLOTLY_AVAILABLE:
                if plot_type == 'loss':
                    # 创建损失函数图表
                    fig = go.Figure()
                    if monitor.loss_history:
                        fig.add_trace(go.Scatter(
                            x=list(range(len(monitor.loss_history))),
                            y=monitor.loss_history,
                            mode='lines+markers',
                            name='Loss',
                            line=dict(color='#4CAF50', width=2),
                            marker=dict(size=4)
                        ))

                    fig.update_layout(
                        title='损失函数变化',
                        xaxis_title='迭代次数',
                        yaxis_title='损失值',
                        template='plotly_dark',
                        height=350,
                        margin=dict(l=50, r=50, t=50, b=50)
                    )

                elif plot_type == 'gradient':
                    # 创建梯度范数图表
                    fig = go.Figure()
                    if monitor.gradient_history:
                        fig.add_trace(go.Scatter(
                            x=list(range(len(monitor.gradient_history))),
                            y=monitor.gradient_history,
                            mode='lines+markers',
                            name='Gradient Norm',
                            line=dict(color='#FF9800', width=2),
                            marker=dict(size=4)
                        ))

                    fig.update_layout(
                        title='梯度范数变化',
                        xaxis_title='迭代次数',
                        yaxis_title='梯度范数',
                        template='plotly_dark',
                        height=350,
                        margin=dict(l=50, r=50, t=50, b=50)
                    )

                # 发送图表数据
                plot_json = json.dumps(fig, cls=plotly.utils.PlotlyJSONEncoder)
                emit('plot_update', {'plot': plot_json})
            else:
                # Plotly不可用时发送简化数据
                emit('plot_update', {'error': 'Plotly不可用'})

        except Exception as e:
            print(f"生成图表时出错: {e}")

# 添加HTTP轮询接口（当SocketIO不可用时）
@app.route('/api/data')
def get_data():
    """获取实时数据（HTTP轮询）"""
    return jsonify(monitor.get_current_data())

def simulate_optimization():
    """模拟优化过程（用于演示）"""
    monitor.start_optimization()
    
    for i in range(1000):
        if not monitor.is_running:
            break
            
        if not monitor.is_paused:
            # 模拟损失函数下降
            noise = np.random.normal(0, 0.1)
            loss = 10.0 * np.exp(-i/100) + noise + 0.1
            gradient_norm = 5.0 * np.exp(-i/50) + abs(noise)
            
            monitor.update_progress(i, loss, gradient_norm)
            
            # 通过Socket.IO发送更新（如果可用）
            if SOCKETIO_AVAILABLE and socketio:
                socketio.emit('update_data', monitor.get_current_data())
            
        time.sleep(1)  # 每秒更新一次

@app.route('/api/start_demo')
def start_demo():
    """启动演示优化"""
    if not monitor.is_running:
        # 在后台线程中运行模拟
        demo_thread = threading.Thread(target=simulate_optimization)
        demo_thread.daemon = True
        demo_thread.start()
        return jsonify({'status': 'demo_started'})
    else:
        return jsonify({'status': 'already_running'})

@app.route('/api/status')
def get_status():
    """获取当前状态"""
    return jsonify(monitor.get_current_data())

def integrate_with_optimizer(optimizer_instance):
    """与实际优化器集成"""
    def callback(iteration, loss, gradient_norm=None, parameters=None):
        """优化器回调函数"""
        monitor.update_progress(iteration, loss, gradient_norm, parameters)
        # 实时发送数据到前端（如果可用）
        if SOCKETIO_AVAILABLE and socketio:
            socketio.emit('update_data', monitor.get_current_data())
    
    return callback

if __name__ == '__main__':
    print("🚀 启动ReaxFF优化监控仪表板...")
    print("📊 访问地址: http://localhost:5000")
    print("🔧 演示模式: http://localhost:5000/api/start_demo")
    print("=" * 50)
    
    # 启动服务器
    if SOCKETIO_AVAILABLE and socketio:
        socketio.run(app, host='0.0.0.0', port=5000, debug=True)
    else:
        print("⚠️ 使用基础Flask服务器（无实时更新）")
        app.run(host='0.0.0.0', port=5000, debug=True)
