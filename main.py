#!/usr/bin/env python3
"""
ReaxFFOpt - ReaxFF力场参数优化工具
主程序入口
"""

import sys
import os
import argparse
from PyQt5.QtWidgets import QApplication

# 导入自定义模块
from gui.main_window import MainWindow
from optimizer.multi_objective import MultiObjectiveOptimizer, MultiFidelityOptimizer

# 尝试导入计算器模块
try:
    from calculator.reaxff_calculator import ReaxFFCalculator
    CALCULATOR_AVAILABLE = True
    print("✅ ReaxFF计算器导入成功")
except ImportError as e:
    print(f"⚠️  ReaxFF计算器不可用: {e}")
    CALCULATOR_AVAILABLE = False
    ReaxFFCalculator = None


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="ReaxFFOpt - ReaxFF力场参数优化工具")
    parser.add_argument("--nogui", action="store_true", help="以命令行模式运行，不启动GUI")
    parser.add_argument("--config", type=str, default="config.json", help="配置文件路径")
    parser.add_argument("--data", type=str, help="训练数据文件夹路径")
    parser.add_argument("--param", type=str, help="初始力场参数文件路径")
    parser.add_argument("--output", type=str, default="results", help="输出结果保存路径")
    
    return parser.parse_args()


def run_cli_mode(args):
    """命令行模式运行"""
    print("在命令行模式下运行ReaxFFOpt...")

    if not CALCULATOR_AVAILABLE:
        print("❌ ReaxFF计算器不可用，无法运行命令行模式")
        print("请安装必要的依赖包（如JAX）或使用GUI模式")
        return

    # 创建ReaxFF计算器
    calculator = ReaxFFCalculator(args.param)

    # 加载训练数据
    training_data = load_training_data(args.data)

    # 定义目标函数
    def energy_rmse(params, data):
        """能量均方根误差目标函数"""
        # 在实际应用中，这里应调用ReaxFF计算器计算能量并与参考数据比较
        return calculator.calculate_energy_rmse(params, data)

    def force_rmse(params, data):
        """力均方根误差目标函数"""
        # 在实际应用中，这里应调用ReaxFF计算器计算力并与参考数据比较
        return calculator.calculate_force_rmse(params, data)

    # 创建多目标优化器
    objectives = [energy_rmse, force_rmse]
    optimizer = MultiObjectiveOptimizer(objectives)

    # 执行优化
    initial_params = calculator.get_parameters()
    results = optimizer.optimize(initial_params, training_data)

    # 保存结果
    os.makedirs(args.output, exist_ok=True)
    save_results(results, os.path.join(args.output, "pareto_solutions.json"))

    # 绘制帕累托前沿
    optimizer.plot_pareto_front(os.path.join(args.output, "pareto_front.png"))

    print(f"优化完成，结果已保存到 {args.output} 文件夹")


def load_training_data(data_path):
    """加载训练数据
    
    Args:
        data_path (str): 数据文件夹路径
        
    Returns:
        dict: 训练数据
    """
    # 在实际应用中，这里应该有完整的数据加载逻辑
    # 现在我们只返回一个空字典作为示例
    print(f"加载训练数据: {data_path}")
    return {}


def save_results(results, output_path):
    """保存优化结果
    
    Args:
        results (list): 优化结果
        output_path (str): 输出文件路径
    """
    # 在实际应用中，这里应该有完整的结果保存逻辑
    print(f"保存优化结果: {output_path}")


def run_gui_mode():
    """GUI模式运行"""
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec_())


def main():
    """程序主入口"""
    # 解析命令行参数
    args = parse_args()
    
    # 根据模式选择运行方式
    if args.nogui:
        run_cli_mode(args)
    else:
        run_gui_mode()


if __name__ == "__main__":
    main() 