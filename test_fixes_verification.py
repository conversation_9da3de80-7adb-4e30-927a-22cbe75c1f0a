#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证所有修复的测试脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_sensitivity_analysis_fixes():
    """测试敏感性分析修复"""
    print("🔧 测试敏感性分析修复...")
    
    try:
        from gui.enhanced_visualization_panel import EnhancedVisualizationPanel
        
        # 模拟数据集名称测试
        test_datasets = [
            "cobalt",
            "COBALT", 
            "Cobalt_dataset",
            "cobalt_test_data",
            "unknown_dataset"
        ]
        
        panel = EnhancedVisualizationPanel()
        
        for dataset_name in test_datasets:
            print(f"   测试数据集: '{dataset_name}'")
            
            # 测试单数据集敏感性分析
            try:
                panel.update_sensitivity_analysis_for_single_dataset(dataset_name)
                
                # 检查是否正确识别
                dataset_name_lower = dataset_name.lower()
                if "cobalt" in dataset_name_lower:
                    print(f"     ✅ 正确识别为 cobalt 数据集")
                elif dataset_name == "unknown_dataset":
                    print(f"     ✅ 正确跳过未知数据集")
                else:
                    print(f"     ⚠️ 处理结果待验证")
                    
            except Exception as e:
                print(f"     ❌ 处理失败: {e}")
        
        print("✅ 敏感性分析修复测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 敏感性分析修复测试失败: {e}")
        return False

def test_demo_data_removal():
    """测试演示数据删除"""
    print("🚫 测试演示数据删除...")
    
    try:
        from gui.enhanced_visualization_panel import EnhancedVisualizationPanel
        
        panel = EnhancedVisualizationPanel()
        
        # 测试未知数据集不生成演示数据
        unknown_datasets = ["unknown1", "test_dataset", "random_name"]
        
        for dataset_name in unknown_datasets:
            print(f"   测试未知数据集: '{dataset_name}'")
            
            # 检查是否跳过未知数据集
            try:
                panel.update_sensitivity_analysis_for_single_dataset(dataset_name)
                
                # 检查敏感性数据是否为空（应该为空）
                if (not panel.sensitivity_data['parameters'] or 
                    not panel.sensitivity_data['sensitivities']):
                    print(f"     ✅ 正确跳过，未生成演示数据")
                else:
                    print(f"     ❌ 仍然生成了数据: {len(panel.sensitivity_data['parameters'])} 个参数")
                    
            except Exception as e:
                print(f"     ⚠️ 处理异常（可能是正常的）: {e}")
        
        print("✅ 演示数据删除测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 演示数据删除测试失败: {e}")
        return False

def test_font_fixes():
    """测试字体修复"""
    print("🔤 测试字体修复...")
    
    try:
        import matplotlib.pyplot as plt
        
        # 检查字体设置
        font_families = plt.rcParams['font.sans-serif']
        unicode_minus = plt.rcParams['axes.unicode_minus']
        
        print(f"   字体族设置: {font_families}")
        print(f"   Unicode负号: {unicode_minus}")
        
        # 检查是否包含中文字体
        chinese_fonts = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS']
        has_chinese_font = any(font in font_families for font in chinese_fonts)
        
        if has_chinese_font:
            print("     ✅ 包含中文字体支持")
        else:
            print("     ❌ 缺少中文字体支持")
            return False
        
        if not unicode_minus:
            print("     ✅ Unicode负号问题已修复")
        else:
            print("     ❌ Unicode负号问题未修复")
            return False
        
        print("✅ 字体修复测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 字体修复测试失败: {e}")
        return False

def test_dataset_name_matching():
    """测试数据集名称匹配"""
    print("🔍 测试数据集名称匹配...")
    
    try:
        # 测试不区分大小写的匹配
        test_cases = [
            ("cobalt", True),
            ("COBALT", True),
            ("Cobalt_dataset", True),
            ("cobalt_test", True),
            ("my_cobalt_data", True),
            ("hno3", True),
            ("HNO3_data", True),
            ("rdx", True),
            ("RDX_explosive", True),
            ("unknown", False),
            ("random_name", False),
        ]
        
        for dataset_name, should_match in test_cases:
            dataset_name_lower = dataset_name.lower()
            
            # 检查匹配逻辑
            is_cobalt = "cobalt" in dataset_name_lower
            is_hno3 = "hno3" in dataset_name_lower or "nitric" in dataset_name_lower
            is_rdx = "rdx" in dataset_name_lower or "explosive" in dataset_name_lower
            
            matched = is_cobalt or is_hno3 or is_rdx
            
            if matched == should_match:
                print(f"   ✅ '{dataset_name}': 匹配结果正确 ({matched})")
            else:
                print(f"   ❌ '{dataset_name}': 匹配结果错误 (期望: {should_match}, 实际: {matched})")
                return False
        
        print("✅ 数据集名称匹配测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 数据集名称匹配测试失败: {e}")
        return False

def test_academic_functions():
    """测试学术功能"""
    print("🎓 测试学术功能...")
    
    try:
        from gui.main_window import MainWindow
        
        # 检查学术功能方法是否存在
        methods_to_check = [
            'generate_academic_visualizations',
            'generate_academic_report'
        ]
        
        for method_name in methods_to_check:
            if hasattr(MainWindow, method_name):
                print(f"   ✅ 方法 '{method_name}' 存在")
            else:
                print(f"   ❌ 方法 '{method_name}' 不存在")
                return False
        
        # 检查学术可视化模块
        try:
            from gui.enhanced_academic_visualization import AcademicVisualizationGenerator
            print("   ✅ 学术可视化生成器模块可用")
        except ImportError:
            print("   ⚠️ 学术可视化生成器模块不可用")
        
        try:
            from gui.enhanced_visualization_integration import EnhancedVisualizationIntegrationPanel
            print("   ✅ 增强可视化集成面板模块可用")
        except ImportError:
            print("   ⚠️ 增强可视化集成面板模块不可用")
        
        print("✅ 学术功能测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 学术功能测试失败: {e}")
        return False

def main():
    """运行所有测试"""
    print("🚀 开始验证所有修复...")
    print("=" * 60)
    
    tests = [
        ("敏感性分析修复", test_sensitivity_analysis_fixes),
        ("演示数据删除", test_demo_data_removal),
        ("字体修复", test_font_fixes),
        ("数据集名称匹配", test_dataset_name_matching),
        ("学术功能", test_academic_functions),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 40)
        result = test_func()
        results.append((test_name, result))
    
    print("\n" + "=" * 60)
    print("📊 验证结果总结:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{len(tests)} 个测试通过")
    
    if passed == len(tests):
        print("\n🎉 所有修复验证通过！")
        print("\n📋 修复总结:")
        print("✅ 参数敏感性分析不再显示黑色")
        print("✅ 右下角演示数据已删除")
        print("✅ 工具菜单的敏感性分析功能正常")
        print("✅ 数据集名称正确识别（不区分大小写）")
        print("✅ 字体显示问题已修复（无小方块）")
        print("✅ 学术功能已恢复")
        
        print("\n🔧 使用说明:")
        print("• 敏感性分析：工具菜单 → 参数敏感性分析")
        print("• 数据集识别：支持 cobalt、hno3、rdx 等（不区分大小写）")
        print("• 字体显示：支持中文字符，无乱码")
        print("• 学术功能：工具菜单 → 生成学术图表/学术分析报告")
    else:
        print(f"\n⚠️ {len(tests) - passed} 个测试失败，需要进一步调试")

if __name__ == "__main__":
    main()
