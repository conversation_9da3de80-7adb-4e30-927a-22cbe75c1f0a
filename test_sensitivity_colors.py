#!/usr/bin/env python3
"""
测试参数敏感性分析颜色显示功能
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_sensitivity_colors():
    """测试敏感性颜色功能"""
    try:
        from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
        from PyQt5.QtCore import Qt
        from PyQt5.QtGui import QColor
        from gui.parameter_panel import ParameterTable
        
        print("🧪 测试参数敏感性颜色显示")
        print("=" * 50)
        
        # 创建应用
        app = QApplication(sys.argv)
        
        # 创建主窗口
        window = QMainWindow()
        window.setWindowTitle("敏感性颜色测试")
        window.resize(800, 600)
        
        # 创建参数表格
        table = ParameterTable()
        
        # 添加测试数据
        test_params = [
            ("p_2_1_1", "2.1500", "1.7", "2.6"),
            ("p_2_1_4", "2.1500", "1.7", "2.6"),
            ("p_2_1_5", "0.1750", "0.0", "0.35"),
            ("p_3_1_1", "100.0000", "80.0", "120.0"),
            ("p_3_1_2", "3.0000", "2.0", "4.0"),
            ("p_4_1_1", "1.2500", "0.5", "2.0"),
        ]
        
        # 设置表格
        table.setRowCount(len(test_params))
        table.setColumnCount(5)
        table.setHorizontalHeaderLabels(["参数名", "当前值", "最小值", "最大值", "敏感性"])
        
        # 填充数据
        for row, (name, value, min_val, max_val) in enumerate(test_params):
            from PyQt5.QtWidgets import QTableWidgetItem
            
            # 参数名
            name_item = QTableWidgetItem(name)
            table.setItem(row, 0, name_item)
            
            # 当前值
            value_item = QTableWidgetItem(value)
            table.setItem(row, 1, value_item)
            
            # 最小值
            min_item = QTableWidgetItem(min_val)
            table.setItem(row, 2, min_item)
            
            # 最大值
            max_item = QTableWidgetItem(max_val)
            table.setItem(row, 3, max_item)
            
            # 敏感性（暂时为空）
            sens_item = QTableWidgetItem("")
            table.setItem(row, 4, sens_item)
        
        # 设置为中央部件
        window.setCentralWidget(table)
        
        # 显示窗口
        window.show()
        
        print("✅ 测试窗口已创建")
        print("📊 现在测试敏感性颜色更新...")
        
        # 模拟敏感性数据
        test_sensitivities = {
            "p_2_1_1": 0.85,  # 高敏感性 - 应该是红色
            "p_2_1_4": 0.65,  # 中敏感性 - 应该是黄色
            "p_2_1_5": 0.25,  # 低敏感性 - 应该是绿色
            "p_3_1_1": 0.75,  # 高敏感性 - 应该是红色
            "p_3_1_2": 0.45,  # 中敏感性 - 应该是黄色
            "p_4_1_1": 0.15,  # 低敏感性 - 应该是绿色
        }
        
        print("🎨 应用敏感性颜色:")
        for param, sens in test_sensitivities.items():
            if sens > 0.7:
                color_desc = "高敏感(红色)"
            elif sens > 0.4:
                color_desc = "中敏感(黄色)"
            else:
                color_desc = "低敏感(绿色)"
            print(f"   {param}: {sens:.3f} - {color_desc}")
        
        # 更新敏感性颜色
        table.update_sensitivities(test_sensitivities)
        
        print("\n✅ 敏感性颜色已应用")
        print("💡 查看表格中的颜色变化:")
        print("   - 红色背景: 高敏感性参数 (>0.7)")
        print("   - 黄色背景: 中敏感性参数 (0.4-0.7)")
        print("   - 绿色背景: 低敏感性参数 (<0.4)")
        print("\n🖱️ 关闭窗口退出测试")
        
        # 运行应用
        return app.exec_()
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        print("请确保已安装PyQt5: pip install PyQt5")
        return 1
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

def main():
    """主函数"""
    print("🎨 参数敏感性颜色显示测试")
    print("=" * 50)
    
    print("📋 测试内容:")
    print("1. 创建参数表格")
    print("2. 添加测试参数")
    print("3. 应用敏感性颜色")
    print("4. 验证颜色显示效果")
    print()
    
    try:
        result = test_sensitivity_colors()
        if result == 0:
            print("\n✅ 测试完成")
        else:
            print("\n⚠️ 测试中断")
        return result
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
        return 0
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        return 1

if __name__ == '__main__':
    sys.exit(main())
