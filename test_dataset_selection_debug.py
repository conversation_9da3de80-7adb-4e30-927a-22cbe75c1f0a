#!/usr/bin/env python3
"""
调试数据集选择功能
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def debug_dataset_selection():
    """调试数据集选择功能"""
    print("🔍 调试数据集选择功能...")
    
    try:
        from PyQt5.QtWidgets import QApplication, QTableWidgetItem
        from PyQt5.QtCore import Qt
        from gui.parameter_panel import ParameterPanel
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建参数面板
        panel = ParameterPanel()
        table = panel.parameter_table
        
        print("✅ 参数面板创建成功")
        
        # 模拟真实的数据集和参数结构
        print("\n📊 模拟真实数据集结构...")
        table.setRowCount(6)
        
        # 第0行：数据集行
        print("   添加数据集行...")
        dataset_checkbox = QTableWidgetItem()
        dataset_checkbox.setFlags(Qt.ItemIsUserCheckable | Qt.ItemIsEnabled)
        dataset_checkbox.setCheckState(Qt.Unchecked)
        # 🔥 关键：设置正确的UserRole数据
        dataset_checkbox.setData(Qt.UserRole, {'type': 'dataset', 'name': 'cobalt', 'expanded': False})
        table.setItem(0, 0, dataset_checkbox)
        
        dataset_name_item = QTableWidgetItem("📊 cobalt数据集")
        dataset_name_item.setData(Qt.UserRole, {'type': 'dataset', 'name': 'cobalt'})
        table.setItem(0, 1, dataset_name_item)
        
        # 其他列填充
        for col in range(2, 6):
            empty_item = QTableWidgetItem("")
            table.setItem(0, col, empty_item)
        
        # 第1-5行：参数行
        param_names = ['p_2_1_1', 'p_2_1_4', 'p_2_1_5', 'p_2_1_9', 'p_2_1_10']
        for i, param_name in enumerate(param_names, 1):
            print(f"   添加参数行: {param_name}")
            
            # 第0列：空（参数行不应该有复选框）
            empty_item = QTableWidgetItem("")
            empty_item.setFlags(Qt.ItemIsEnabled)
            table.setItem(i, 0, empty_item)
            
            # 第1列：参数名
            param_item = QTableWidgetItem(f"└─ {param_name}")
            param_item.setData(Qt.UserRole, {
                'type': 'parameter', 
                'name': param_name,
                'dataset': 'cobalt'
            })
            table.setItem(i, 1, param_item)
            
            # 第2列：当前值
            value_item = QTableWidgetItem("2.1500")
            table.setItem(i, 2, value_item)
            
            # 第3列：范围
            range_item = QTableWidgetItem("1.70 - 2.60")
            table.setItem(i, 3, range_item)
            
            # 第4列：优化复选框
            opt_checkbox = QTableWidgetItem()
            opt_checkbox.setFlags(Qt.ItemIsUserCheckable | Qt.ItemIsEnabled)
            opt_checkbox.setCheckState(Qt.Unchecked)
            table.setItem(i, 4, opt_checkbox)
            
            # 第5列：敏感性
            sens_item = QTableWidgetItem("0.00")
            table.setItem(i, 5, sens_item)
        
        print("✅ 数据结构创建完成")
        
        # 调试：检查初始状态
        print("\n🔍 检查初始状态...")
        print(f"   数据集复选框状态: {dataset_checkbox.checkState()}")
        print(f"   数据集UserRole: {dataset_checkbox.data(Qt.UserRole)}")
        
        unchecked_count = 0
        for i in range(1, 6):
            opt_checkbox = table.item(i, 4)
            if opt_checkbox and opt_checkbox.checkState() == Qt.Unchecked:
                unchecked_count += 1
        print(f"   未勾选的参数数量: {unchecked_count}/5")
        
        # 测试：手动勾选数据集
        print("\n🔄 测试数据集选择功能...")
        print("   勾选数据集复选框...")
        
        # 勾选数据集
        dataset_checkbox.setCheckState(Qt.Checked)
        
        # 手动触发信号处理
        print("   触发信号处理...")
        panel._on_table_item_changed(dataset_checkbox)
        
        # 检查结果
        print("\n📊 检查结果...")
        checked_count = 0
        for i in range(1, 6):
            opt_checkbox = table.item(i, 4)
            param_name = table.item(i, 1).text()
            if opt_checkbox and opt_checkbox.checkState() == Qt.Checked:
                checked_count += 1
                print(f"     ✅ 参数已勾选: {param_name}")
            else:
                print(f"     ❌ 参数未勾选: {param_name}")
                # 调试：检查复选框状态
                if opt_checkbox:
                    print(f"        复选框存在，状态: {opt_checkbox.checkState()}")
                    print(f"        复选框标志: {opt_checkbox.flags()}")
                else:
                    print(f"        复选框不存在！")
        
        print(f"\n📈 结果统计: {checked_count}/5 个参数被自动勾选")
        
        # 测试参数获取
        print("\n🔍 测试参数获取功能...")
        selected_params = table.get_parameters_for_optimization()
        print(f"   获取到的参数数量: {len(selected_params)}")
        for name, info in selected_params.items():
            print(f"     {name}: {info}")
        
        # 显示面板供手动验证
        panel.show()
        panel.resize(1200, 600)
        
        print("\n🎉 调试完成！面板已显示，请手动验证：")
        print("   1. 检查参数行是否只有一个复选框（第4列）")
        print("   2. 尝试勾选/取消勾选数据集复选框")
        print("   3. 观察参数是否自动勾选/取消勾选")
        
        return checked_count == 5
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 数据集选择功能调试")
    print("=" * 50)
    
    success = debug_dataset_selection()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 数据集选择功能正常！")
    else:
        print("⚠️  数据集选择功能存在问题")
    
    # 保持窗口打开
    try:
        from PyQt5.QtWidgets import QApplication
        app = QApplication.instance()
        if app:
            print("\n💡 窗口将保持打开，按Ctrl+C退出")
            app.exec_()
    except KeyboardInterrupt:
        print("\n👋 退出调试")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
