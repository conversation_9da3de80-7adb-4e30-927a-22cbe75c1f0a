#!/usr/bin/env python3
"""
监控集成示例
展示如何在ReaxFF优化中集成真实监控
"""

import os
import sys
import time
import threading
import requests
import numpy as np
from typing import Dict, Any

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def start_dashboard_if_needed():
    """如果需要，启动仪表板"""
    try:
        # 检查仪表板是否已经运行
        response = requests.get('http://localhost:5000/api/data', timeout=2)
        print("✅ 仪表板已在运行")
        return True
    except:
        print("🚀 启动监控仪表板...")
        # 启动仪表板
        import subprocess
        dashboard_process = subprocess.Popen([
            sys.executable, 'simple_dashboard.py'
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # 等待启动
        time.sleep(3)
        
        # 再次检查
        try:
            response = requests.get('http://localhost:5000/api/data', timeout=2)
            print("✅ 仪表板启动成功")
            return True
        except:
            print("❌ 仪表板启动失败")
            return False

def send_optimization_start(dataset_info: Dict, selected_params: Dict, method: str = "ReaxFF优化"):
    """发送优化开始信号"""
    try:
        data = {
            'dataset_info': dataset_info,
            'selected_parameters': selected_params,
            'optimization_method': method
        }
        response = requests.post('http://localhost:5000/api/start_real_optimization', 
                               json=data, timeout=5)
        if response.status_code == 200:
            print(f"✅ 已通知仪表板开始监控: {method}")
            return True
        else:
            print(f"⚠️ 通知仪表板失败: {response.text}")
            return False
    except Exception as e:
        print(f"⚠️ 发送优化开始信号失败: {e}")
        return False

def send_progress_update(iteration: int, loss: float):
    """发送进度更新"""
    try:
        data = {
            'iteration': iteration,
            'loss': loss
        }
        response = requests.post('http://localhost:5000/api/update_progress', 
                               json=data, timeout=2)
        return response.status_code == 200
    except:
        return False

def send_optimization_stop():
    """发送优化停止信号"""
    try:
        data = {'action': 'stop'}
        response = requests.post('http://localhost:5000/api/control', 
                               json=data, timeout=2)
        return response.status_code == 200
    except:
        return False

class MonitoredOptimizer:
    """带监控的优化器示例"""
    
    def __init__(self, enable_monitoring=True):
        self.enable_monitoring = enable_monitoring
        self.dashboard_available = False
        
        if self.enable_monitoring:
            self.dashboard_available = start_dashboard_if_needed()
    
    def optimize(self, initial_params: Dict, dataset_info: Dict, max_iterations: int = 100):
        """执行优化"""
        print(f"🚀 开始优化，参数数量: {len(initial_params)}")
        
        # 通知仪表板开始监控
        if self.dashboard_available:
            send_optimization_start(dataset_info, initial_params, "示例优化器")
        
        best_loss = float('inf')
        best_params = initial_params.copy()
        
        try:
            for iteration in range(max_iterations):
                # 模拟优化步骤
                current_params = self._optimization_step(initial_params, iteration)
                
                # 计算损失函数
                loss = self._compute_loss(current_params)
                
                # 更新最佳结果
                if loss < best_loss:
                    best_loss = loss
                    best_params = current_params.copy()
                
                # 发送进度更新
                if self.dashboard_available:
                    send_progress_update(iteration, loss)
                
                # 打印进度
                if iteration % 10 == 0:
                    print(f"📊 迭代 {iteration}: 损失 = {loss:.6f}, 最佳 = {best_loss:.6f}")
                
                # 模拟计算时间
                time.sleep(0.1)
                
                # 收敛检查
                if loss < 0.01:
                    print(f"✅ 在第 {iteration} 次迭代达到收敛")
                    break
            
            print(f"🎉 优化完成，最佳损失: {best_loss:.6f}")
            return best_params, best_loss
            
        except KeyboardInterrupt:
            print("\n⏹️ 优化被用户中断")
            return best_params, best_loss
        finally:
            # 通知仪表板停止监控
            if self.dashboard_available:
                send_optimization_stop()
    
    def _optimization_step(self, params: Dict, iteration: int) -> Dict:
        """单步优化（模拟）"""
        new_params = {}
        for name, value in params.items():
            # 模拟参数更新
            perturbation = 0.01 * np.random.normal() * np.exp(-iteration/50)
            new_params[name] = value + perturbation
        return new_params
    
    def _compute_loss(self, params: Dict) -> float:
        """计算损失函数（模拟）"""
        # 模拟一个有噪声的二次函数
        loss = 0.0
        for name, value in params.items():
            target = 2.0  # 假设目标值
            loss += (value - target) ** 2
        
        # 添加噪声
        loss += 0.1 * np.random.normal()
        
        # 确保损失为正
        return max(0.001, loss)

def integrate_with_parameter_panel_example():
    """与参数面板集成的示例"""
    print("🔧 参数面板集成示例")
    print("=" * 50)
    
    # 模拟从参数面板获取的数据
    dataset_info = {
        "cobalt": "钴数据集",
        "training_data": "训练数据集"
    }
    
    selected_params = {
        "p_2_1_1": {"value": 2.15, "min": 1.7, "max": 2.6, "dataset": "cobalt"},
        "p_2_1_4": {"value": 2.15, "min": 1.7, "max": 2.6, "dataset": "cobalt"},
        "p_2_1_5": {"value": 2.15, "min": 1.7, "max": 2.6, "dataset": "cobalt"},
        "p_3_1_1": {"value": 100.0, "min": 80.0, "max": 120.0, "dataset": "cobalt"},
        "p_3_1_2": {"value": 3.0, "min": 2.0, "max": 4.0, "dataset": "cobalt"}
    }
    
    print(f"📊 数据集: {list(dataset_info.keys())}")
    print(f"🎯 参数数量: {len(selected_params)}")
    
    # 创建监控优化器
    optimizer = MonitoredOptimizer(enable_monitoring=True)
    
    # 提取参数值
    initial_params = {name: info["value"] for name, info in selected_params.items()}
    
    # 执行优化
    best_params, best_loss = optimizer.optimize(initial_params, dataset_info, max_iterations=50)
    
    print(f"\n🎉 优化结果:")
    print(f"   最佳损失: {best_loss:.6f}")
    print(f"   最佳参数: {best_params}")

def main():
    """主函数"""
    print("🚀 ReaxFF优化监控集成示例")
    print("=" * 50)
    
    print("💡 此示例展示如何:")
    print("   1. 启动监控仪表板")
    print("   2. 在优化过程中发送真实数据")
    print("   3. 实时监控优化进度")
    print("   4. 区分演示模式和真实优化")
    print()
    
    try:
        # 运行集成示例
        integrate_with_parameter_panel_example()
        
        print("\n" + "=" * 50)
        print("✅ 示例运行完成！")
        print()
        print("🌐 监控仪表板地址: http://localhost:5000")
        print("📊 您可以在浏览器中查看实时监控数据")
        print()
        print("💡 在实际使用中:")
        print("   1. 在优化器中调用 send_progress_update(iteration, loss)")
        print("   2. 或使用 MonitoredOptimizer 类")
        print("   3. 或集成到现有的参数面板中")
        
    except KeyboardInterrupt:
        print("\n👋 示例被用户中断")
    except Exception as e:
        print(f"\n❌ 示例运行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
    input("\n按回车键退出...")
