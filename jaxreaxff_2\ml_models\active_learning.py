import jax
import jax.numpy as jnp
import numpy as np
from typing import Dict, List, Tuple, Callable, Optional, Union, Any
import flax.linen as nn
import optax
from functools import partial
import scipy.spatial.distance as distance
from sklearn.cluster import KMeans

class UncertaintySampling:
    """
    基于不确定性的主动学习采样策略
    
    通过测量模型预测的不确定性来选择最有价值的样本进行标记
    """
    
    def __init__(
        self,
        uncertainty_method: str = 'ensemble',
        n_estimators: int = 5,
        dropout_rate: float = 0.1,
        n_dropout_samples: int = 20,
        beta: float = 1.0,
        seed: Optional[int] = None
    ):
        """
        初始化不确定性采样器
        
        Args:
            uncertainty_method: 不确定性估计方法，可选值：'ensemble', 'dropout', 'bootstrap'
            n_estimators: 集成模型的数量（对于ensemble方法）
            dropout_rate: Dropout比率（对于dropout方法）
            n_dropout_samples: Dropout采样次数
            beta: 用于平衡探索与利用的权重参数
            seed: 随机种子
        """
        self.uncertainty_method = uncertainty_method
        self.n_estimators = n_estimators
        self.dropout_rate = dropout_rate
        self.n_dropout_samples = n_dropout_samples
        self.beta = beta
        
        # 初始化随机数生成器
        if seed is not None:
            self.rng = jax.random.PRNGKey(seed)
        else:
            self.rng = jax.random.PRNGKey(int(np.random.randint(0, 2**32)))
    
    def _get_next_rng(self) -> jax.random.PRNGKey:
        """获取下一个随机数生成器"""
        self.rng, next_rng = jax.random.split(self.rng)
        return next_rng
    
    def estimate_uncertainty(
        self,
        model: nn.Module,
        params: Dict,
        inputs: jnp.ndarray,
        batch_size: int = 32,
    ) -> jnp.ndarray:
        """
        估计模型预测的不确定性
        
        Args:
            model: 模型
            params: 模型参数
            inputs: 输入特征
            batch_size: 批量大小
            
        Returns:
            uncertainty: 每个样本的不确定性估计 [n_samples]
        """
        n_samples = inputs.shape[0]
        
        if self.uncertainty_method == 'ensemble':
            return self._ensemble_uncertainty(model, params, inputs, batch_size)
        elif self.uncertainty_method == 'dropout':
            return self._dropout_uncertainty(model, params, inputs, batch_size)
        elif self.uncertainty_method == 'bootstrap':
            return self._bootstrap_uncertainty(model, params, inputs, batch_size)
        else:
            raise ValueError(f"Unsupported uncertainty method: {self.uncertainty_method}")
    
    def _ensemble_uncertainty(
        self,
        model: nn.Module,
        params_list: List[Dict],
        inputs: jnp.ndarray,
        batch_size: int = 32,
    ) -> jnp.ndarray:
        """
        使用集成模型估计不确定性
        
        Args:
            model: 模型
            params_list: 模型参数列表（每个集成成员一个）
            inputs: 输入特征
            batch_size: 批量大小
            
        Returns:
            uncertainty: 每个样本的不确定性估计 [n_samples]
        """
        n_samples = inputs.shape[0]
        n_batches = (n_samples + batch_size - 1) // batch_size
        
        # 存储每个集成成员的预测
        all_predictions = []
        
        # 对每个集成成员进行预测
        for params in params_list:
            predictions = []
            
            # 批量处理样本
            for i in range(n_batches):
                start_idx = i * batch_size
                end_idx = min(start_idx + batch_size, n_samples)
                batch_inputs = inputs[start_idx:end_idx]
                
                # 预测
                batch_preds = model.apply({'params': params}, batch_inputs, training=False)
                predictions.append(batch_preds)
            
            # 合并批次预测
            ensemble_preds = jnp.concatenate(predictions, axis=0)
            all_predictions.append(ensemble_preds)
        
        # 堆叠所有预测 [n_estimators, n_samples, output_dim]
        stacked_preds = jnp.stack(all_predictions, axis=0)
        
        # 计算预测均值和方差
        mean_preds = jnp.mean(stacked_preds, axis=0)  # [n_samples, output_dim]
        var_preds = jnp.var(stacked_preds, axis=0)  # [n_samples, output_dim]
        
        # 计算总不确定性（预测方差的和）
        total_uncertainty = jnp.sum(var_preds, axis=-1)  # [n_samples]
        
        return total_uncertainty
    
    def _dropout_uncertainty(
        self,
        model: nn.Module,
        params: Dict,
        inputs: jnp.ndarray,
        batch_size: int = 32,
    ) -> jnp.ndarray:
        """
        使用MC Dropout估计不确定性
        
        Args:
            model: 模型
            params: 模型参数
            inputs: 输入特征
            batch_size: 批量大小
            
        Returns:
            uncertainty: 每个样本的不确定性估计 [n_samples]
        """
        n_samples = inputs.shape[0]
        n_batches = (n_samples + batch_size - 1) // batch_size
        
        # 定义带有dropout的预测函数
        @partial(jax.jit, static_argnums=(0,))
        def predict_with_dropout(model, params, inputs, dropout_rng):
            return model.apply(
                {'params': params},
                inputs,
                training=True,  # 启用dropout
                rngs={'dropout': dropout_rng}
            )
        
        # 存储多次dropout预测结果
        all_predictions = []
        
        # 执行多次MC Dropout预测
        for _ in range(self.n_dropout_samples):
            predictions = []
            dropout_rng = self._get_next_rng()
            
            # 批量处理样本
            for i in range(n_batches):
                start_idx = i * batch_size
                end_idx = min(start_idx + batch_size, n_samples)
                batch_inputs = inputs[start_idx:end_idx]
                
                # 预测
                batch_preds = predict_with_dropout(model, params, batch_inputs, dropout_rng)
                predictions.append(batch_preds)
            
            # 合并批次预测
            dropout_preds = jnp.concatenate(predictions, axis=0)
            all_predictions.append(dropout_preds)
        
        # 堆叠所有预测 [n_dropout_samples, n_samples, output_dim]
        stacked_preds = jnp.stack(all_predictions, axis=0)
        
        # 计算预测均值和方差
        mean_preds = jnp.mean(stacked_preds, axis=0)  # [n_samples, output_dim]
        var_preds = jnp.var(stacked_preds, axis=0)  # [n_samples, output_dim]
        
        # 计算总不确定性（预测方差的和）
        total_uncertainty = jnp.sum(var_preds, axis=-1)  # [n_samples]
        
        return total_uncertainty
    
    def _bootstrap_uncertainty(
        self,
        model: nn.Module,
        params_list: List[Dict],
        inputs: jnp.ndarray,
        batch_size: int = 32,
    ) -> jnp.ndarray:
        """
        使用bootstrap方法估计不确定性
        
        Args:
            model: 模型
            params_list: bootstrap模型参数列表
            inputs: 输入特征
            batch_size: 批量大小
            
        Returns:
            uncertainty: 每个样本的不确定性估计 [n_samples]
        """
        # Bootstrap方法实现与集成方法类似
        return self._ensemble_uncertainty(model, params_list, inputs, batch_size)
    
    def select_samples(
        self,
        model: nn.Module,
        params: Dict,
        unlabeled_pool: jnp.ndarray,
        n_select: int = 10,
        batch_size: int = 32,
    ) -> jnp.ndarray:
        """
        从未标记池中选择最不确定的样本
        
        Args:
            model: 模型
            params: 模型参数
            unlabeled_pool: 未标记数据池 [n_unlabeled, feature_dim]
            n_select: 要选择的样本数量
            batch_size: 批量大小
            
        Returns:
            selected_indices: 选定样本的索引 [n_select]
        """
        # 估计未标记样本的不确定性
        uncertainties = self.estimate_uncertainty(model, params, unlabeled_pool, batch_size)
        
        # 选择不确定性最高的样本
        selected_indices = jnp.argsort(uncertainties)[-n_select:]
        
        return selected_indices
    
    def explore_exploit_selection(
        self,
        model: nn.Module,
        params: Dict,
        unlabeled_pool: jnp.ndarray,
        n_select: int = 10,
        batch_size: int = 32,
    ) -> jnp.ndarray:
        """
        使用探索-利用平衡的策略选择样本
        
        Args:
            model: 模型
            params: 模型参数
            unlabeled_pool: 未标记数据池 [n_unlabeled, feature_dim]
            n_select: 要选择的样本数量
            batch_size: 批量大小
            
        Returns:
            selected_indices: 选定样本的索引 [n_select]
        """
        # 估计未标记样本的不确定性
        uncertainties = self.estimate_uncertainty(model, params, unlabeled_pool, batch_size)
        
        # 进行预测
        n_samples = unlabeled_pool.shape[0]
        n_batches = (n_samples + batch_size - 1) // batch_size
        
        predictions = []
        for i in range(n_batches):
            start_idx = i * batch_size
            end_idx = min(start_idx + batch_size, n_samples)
            batch_inputs = unlabeled_pool[start_idx:end_idx]
            
            batch_preds = model.apply({'params': params}, batch_inputs, training=False)
            predictions.append(batch_preds)
        
        predictions = jnp.concatenate(predictions, axis=0)
        
        # 计算期望改进（Expected Improvement）
        # 这里使用一个简单的公式：EI = uncertainty + beta * predicted_value
        expected_improvement = uncertainties + self.beta * jnp.abs(predictions)
        
        # 选择EI最高的样本
        selected_indices = jnp.argsort(expected_improvement)[-n_select:]
        
        return selected_indices


class DiversitySampling:
    """
    基于多样性的主动学习采样策略
    
    通过选择多样化的样本来提高模型的泛化能力
    """
    
    def __init__(
        self,
        diversity_method: str = 'kmeans',
        distance_metric: str = 'euclidean',
        n_clusters: int = 10,
        alpha: float = 0.5,
        seed: Optional[int] = None
    ):
        """
        初始化多样性采样器
        
        Args:
            diversity_method: 多样性估计方法，可选值：'kmeans', 'greedy'
            distance_metric: 距离度量方法
            n_clusters: 聚类数量（对于kmeans方法）
            alpha: 多样性权重
            seed: 随机种子
        """
        self.diversity_method = diversity_method
        self.distance_metric = distance_metric
        self.n_clusters = n_clusters
        self.alpha = alpha
        self.seed = seed
    
    def _compute_pairwise_distances(
        self,
        features: jnp.ndarray,
        metric: str = 'euclidean'
    ) -> jnp.ndarray:
        """
        计算特征之间的成对距离
        
        Args:
            features: 特征矩阵 [n_samples, feature_dim]
            metric: 距离度量
            
        Returns:
            distances: 距离矩阵 [n_samples, n_samples]
        """
        # 转换为numpy数组以使用scipy的距离函数
        features_np = np.array(features)
        
        # 计算距离矩阵
        dist_matrix = distance.squareform(distance.pdist(features_np, metric=metric))
        
        return jnp.array(dist_matrix)
    
    def kmeans_sampling(
        self,
        features: jnp.ndarray,
        n_select: int = 10
    ) -> jnp.ndarray:
        """
        使用K-means聚类进行多样性采样
        
        Args:
            features: 特征矩阵 [n_samples, feature_dim]
            n_select: 要选择的样本数量
            
        Returns:
            selected_indices: 选定样本的索引 [n_select]
        """
        # 转换为numpy数组
        features_np = np.array(features)
        
        # 确定聚类数量
        k = min(self.n_clusters, n_select)
        
        # 执行K-means聚类
        kmeans = KMeans(n_clusters=k, random_state=self.seed, n_init=10)
        cluster_assignments = kmeans.fit_predict(features_np)
        
        # 从每个聚类中选择最接近质心的样本
        selected_indices = []
        cluster_centers = kmeans.cluster_centers_
        
        for i in range(k):
            # 获取当前聚类的所有样本
            cluster_samples = np.where(cluster_assignments == i)[0]
            
            if len(cluster_samples) > 0:
                # 计算到质心的距离
                samples_features = features_np[cluster_samples]
                distances = np.sum((samples_features - cluster_centers[i])**2, axis=1)
                
                # 选择最接近质心的样本
                closest_idx = cluster_samples[np.argmin(distances)]
                selected_indices.append(closest_idx)
        
        # 如果需要选择更多样本，从剩余样本中选择
        if len(selected_indices) < n_select:
            # 计算所有样本到已选样本的最小距离
            remaining_indices = np.setdiff1d(np.arange(len(features)), selected_indices)
            
            while len(selected_indices) < n_select and len(remaining_indices) > 0:
                # 计算剩余样本到已选样本的距离
                min_distances = []
                
                for idx in remaining_indices:
                    # 计算到所有已选样本的距离
                    dists = [np.sum((features_np[idx] - features_np[sel_idx])**2) for sel_idx in selected_indices]
                    min_distances.append(min(dists))
                
                # 选择距离最大的样本
                max_dist_idx = np.argmax(min_distances)
                selected_indices.append(remaining_indices[max_dist_idx])
                
                # 更新剩余样本
                remaining_indices = np.setdiff1d(remaining_indices, [remaining_indices[max_dist_idx]])
        
        return jnp.array(selected_indices)
    
    def greedy_sampling(
        self,
        features: jnp.ndarray,
        n_select: int = 10
    ) -> jnp.ndarray:
        """
        使用贪婪算法进行多样性采样
        
        Args:
            features: 特征矩阵 [n_samples, feature_dim]
            n_select: 要选择的样本数量
            
        Returns:
            selected_indices: 选定样本的索引 [n_select]
        """
        # 转换为numpy数组
        features_np = np.array(features)
        n_samples = len(features_np)
        
        # 计算距离矩阵
        dist_matrix = distance.squareform(distance.pdist(features_np, metric=self.distance_metric))
        
        # 初始化已选和未选样本集
        selected_indices = []
        remaining_indices = list(range(n_samples))
        
        # 选择第一个样本（可以随机选择或选择距离原点最远的样本）
        if self.seed is not None:
            np.random.seed(self.seed)
            
        # 随机选择第一个样本
        first_idx = np.random.choice(remaining_indices)
        selected_indices.append(first_idx)
        remaining_indices.remove(first_idx)
        
        # 贪婪选择剩余样本
        while len(selected_indices) < n_select and remaining_indices:
            # 计算每个未选样本到已选样本的最小距离
            min_distances = []
            
            for idx in remaining_indices:
                # 计算到所有已选样本的距离
                dists = [dist_matrix[idx, sel_idx] for sel_idx in selected_indices]
                min_distances.append(min(dists))
            
            # 选择具有最大最小距离的样本
            max_min_dist_idx = np.argmax(min_distances)
            next_idx = remaining_indices[max_min_dist_idx]
            
            selected_indices.append(next_idx)
            remaining_indices.remove(next_idx)
        
        return jnp.array(selected_indices)
    
    def select_samples(
        self,
        features: jnp.ndarray,
        n_select: int = 10
    ) -> jnp.ndarray:
        """
        从特征集中选择多样化的样本
        
        Args:
            features: 特征矩阵 [n_samples, feature_dim]
            n_select: 要选择的样本数量
            
        Returns:
            selected_indices: 选定样本的索引 [n_select]
        """
        if self.diversity_method == 'kmeans':
            return self.kmeans_sampling(features, n_select)
        elif self.diversity_method == 'greedy':
            return self.greedy_sampling(features, n_select)
        else:
            raise ValueError(f"Unsupported diversity method: {self.diversity_method}")


class ActiveLearningManager:
    """
    主动学习管理器，组合不确定性和多样性采样
    
    用于协调主动学习过程，包括样本选择、训练和评估
    """
    
    def __init__(
        self,
        uncertainty_sampler: Optional[UncertaintySampling] = None,
        diversity_sampler: Optional[DiversitySampling] = None,
        uncertainty_weight: float = 0.7,
        diversity_weight: float = 0.3,
        feature_extractor: Optional[Callable] = None,
        seed: Optional[int] = None
    ):
        """
        初始化主动学习管理器
        
        Args:
            uncertainty_sampler: 不确定性采样器
            diversity_sampler: 多样性采样器
            uncertainty_weight: 不确定性权重
            diversity_weight: 多样性权重
            feature_extractor: 特征提取器函数
            seed: 随机种子
        """
        # 设置采样器
        self.uncertainty_sampler = uncertainty_sampler or UncertaintySampling(seed=seed)
        self.diversity_sampler = diversity_sampler or DiversitySampling(seed=seed)
        
        # 设置权重
        self.uncertainty_weight = uncertainty_weight
        self.diversity_weight = diversity_weight
        
        # 特征提取器
        self.feature_extractor = feature_extractor
        
        # 初始化随机数生成器
        if seed is not None:
            self.rng = jax.random.PRNGKey(seed)
        else:
            self.rng = jax.random.PRNGKey(int(np.random.randint(0, 2**32)))
    
    def _get_next_rng(self) -> jax.random.PRNGKey:
        """获取下一个随机数生成器"""
        self.rng, next_rng = jax.random.split(self.rng)
        return next_rng
    
    def select_samples(
        self,
        model: nn.Module,
        params: Dict,
        unlabeled_pool: jnp.ndarray,
        labeled_indices: Optional[jnp.ndarray] = None,
        n_select: int = 10,
        batch_size: int = 32,
    ) -> jnp.ndarray:
        """
        从未标记池中选择样本进行标记
        
        Args:
            model: 模型
            params: 模型参数
            unlabeled_pool: 未标记数据池 [n_unlabeled, feature_dim]
            labeled_indices: 已标记样本索引
            n_select: 要选择的样本数量
            batch_size: 批量大小
            
        Returns:
            selected_indices: 选定样本的索引 [n_select]
        """
        # 提取特征（如果提供了特征提取器）
        if self.feature_extractor is not None:
            features = self.feature_extractor(unlabeled_pool)
        else:
            features = unlabeled_pool
        
        # 计算不确定性分数
        uncertainty_scores = self.uncertainty_sampler.estimate_uncertainty(
            model, params, unlabeled_pool, batch_size
        )
        
        # 归一化不确定性分数
        uncertainty_scores = (uncertainty_scores - jnp.min(uncertainty_scores)) / (jnp.max(uncertainty_scores) - jnp.min(uncertainty_scores) + 1e-8)
        
        # 计算多样性分数
        # 如果已经有标记样本，考虑与它们的多样性
        if labeled_indices is not None and len(labeled_indices) > 0:
            # 计算到最近标记样本的距离
            features_np = np.array(features)
            labeled_features = features_np[labeled_indices]
            
            # 计算每个未标记样本到最近标记样本的距离
            diversity_scores = []
            for feat in features_np:
                # 计算到所有标记样本的距离
                dists = np.sum((labeled_features - feat)**2, axis=1)
                # 最近距离作为多样性分数
                diversity_scores.append(np.min(dists))
            
            diversity_scores = jnp.array(diversity_scores)
        else:
            # 如果没有标记样本，使用普通的多样性采样
            # 这里我们使用与K-means中心的距离作为多样性分数
            k = min(10, len(features))
            kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)
            features_np = np.array(features)
            kmeans.fit(features_np)
            
            # 计算到最近聚类中心的距离
            diversity_scores = []
            for i, feat in enumerate(features_np):
                # 计算到所有聚类中心的距离
                dists = np.sum((kmeans.cluster_centers_ - feat)**2, axis=1)
                # 最近距离作为多样性分数
                diversity_scores.append(np.min(dists))
            
            diversity_scores = jnp.array(diversity_scores)
        
        # 归一化多样性分数
        diversity_scores = (diversity_scores - jnp.min(diversity_scores)) / (jnp.max(diversity_scores) - jnp.min(diversity_scores) + 1e-8)
        
        # 计算总分数（加权组合）
        total_scores = (self.uncertainty_weight * uncertainty_scores + 
                        self.diversity_weight * diversity_scores)
        
        # 选择总分数最高的样本
        selected_indices = jnp.argsort(total_scores)[-n_select:]
        
        return selected_indices
    
    def select_samples_for_annotation(
        self,
        model: nn.Module,
        params: Dict,
        unlabeled_pool: Dict[str, jnp.ndarray],
        labeled_indices: Optional[jnp.ndarray] = None,
        n_select: int = 10,
        batch_size: int = 32,
    ) -> jnp.ndarray:
        """
        选择分子系统样本进行标注
        
        Args:
            model: 模型
            params: 模型参数
            unlabeled_pool: 未标记数据池，包含'atom_types', 'positions'等键
            labeled_indices: 已标记样本索引
            n_select: 要选择的样本数量
            batch_size: 批量大小
            
        Returns:
            selected_indices: 选定样本的索引 [n_select]
        """
        # 对于分子系统，我们需要从特征中提取有意义的表示
        # 这里我们假设model有一个特征提取方法
        def extract_molecule_features(inputs):
            atom_types = inputs['atom_types']
            positions = inputs['positions']
            
            # 使用模型提取特征
            features = model.apply(
                {'params': params},
                atom_types,
                positions,
                training=False,
                return_features=True
            )
            
            return features
        
        # 提取特征
        features = []
        n_samples = unlabeled_pool['atom_types'].shape[0]
        n_batches = (n_samples + batch_size - 1) // batch_size
        
        for i in range(n_batches):
            start_idx = i * batch_size
            end_idx = min(start_idx + batch_size, n_samples)
            
            batch_inputs = {
                k: v[start_idx:end_idx] for k, v in unlabeled_pool.items()
            }
            
            batch_features = extract_molecule_features(batch_inputs)
            features.append(batch_features)
        
        features = jnp.concatenate(features, axis=0)
        
        # 使用提取的特征进行样本选择
        return self.select_samples(
            model, params, features, labeled_indices, n_select, batch_size
        )
    
    def active_learning_cycle(
        self,
        model: nn.Module,
        params: Dict,
        train_fn: Callable,
        labeled_data: Dict[str, jnp.ndarray],
        unlabeled_pool: Dict[str, jnp.ndarray],
        n_select: int = 10,
        n_cycles: int = 5,
        eval_fn: Optional[Callable] = None,
        verbose: bool = True
    ) -> Tuple[Dict, List[Dict]]:
        """
        执行完整的主动学习循环
        
        Args:
            model: 模型
            params: 初始模型参数
            train_fn: 训练函数，接受参数(model, labeled_data)，返回更新的参数
            labeled_data: 已标记数据
            unlabeled_pool: 未标记数据池
            n_select: 每个循环选择的样本数量
            n_cycles: 主动学习循环次数
            eval_fn: 评估函数，接受参数(model, params, eval_data)，返回评估指标
            verbose: 是否打印进度信息
            
        Returns:
            final_params: 最终模型参数
            metrics_history: 每个循环的评估指标
        """
        current_params = params
        metrics_history = []
        
        # 初始化已标记样本索引
        labeled_indices = jnp.arange(labeled_data['atom_types'].shape[0])
        
        # 初始化未标记样本索引
        all_indices = jnp.arange(unlabeled_pool['atom_types'].shape[0])
        unlabeled_indices = all_indices
        
        for cycle in range(n_cycles):
            if verbose:
                print(f"Active Learning Cycle {cycle+1}/{n_cycles}")
                print(f"Labeled samples: {len(labeled_indices)}")
            
            # 训练模型
            current_params = train_fn(model, current_params, labeled_data)
            
            # 评估模型
            metrics = {}
            if eval_fn is not None:
                metrics = eval_fn(model, current_params)
                metrics_history.append(metrics)
                
                if verbose:
                    print(f"Evaluation metrics: {metrics}")
            
            # 选择新样本
            if cycle < n_cycles - 1:  # 最后一个循环不需要选择新样本
                selected_indices = self.select_samples_for_annotation(
                    model, current_params, 
                    {k: v[unlabeled_indices] for k, v in unlabeled_pool.items()},
                    labeled_indices, n_select
                )
                
                # 获取全局索引
                global_selected_indices = unlabeled_indices[selected_indices]
                
                # 更新已标记和未标记样本集
                labeled_indices = jnp.concatenate([labeled_indices, global_selected_indices])
                unlabeled_indices = jnp.array([idx for idx in unlabeled_indices if idx not in global_selected_indices])
                
                # 更新已标记数据
                labeled_data = {
                    k: jnp.concatenate([labeled_data[k], unlabeled_pool[k][global_selected_indices]])
                    for k in labeled_data
                }
                
                if verbose:
                    print(f"Selected {len(selected_indices)} new samples")
                    print(f"Updated labeled samples: {len(labeled_indices)}")
        
        # 最终训练和评估
        final_params = train_fn(model, current_params, labeled_data)
        
        if eval_fn is not None:
            final_metrics = eval_fn(model, final_params)
            metrics_history.append(final_metrics)
            
            if verbose:
                print(f"Final evaluation metrics: {final_metrics}")
        
        return final_params, metrics_history 