#!/usr/bin/env python3
"""
真实优化过程监控集成
连接ReaxFF优化器与监控仪表板
"""

import os
import sys
import time
import threading
import json
from typing import Dict, Any, Optional, Callable
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class RealTimeMonitor:
    """真实时间监控器"""
    
    def __init__(self):
        self.is_monitoring = False
        self.optimization_data = {
            'start_time': None,
            'current_iteration': 0,
            'current_loss': float('inf'),
            'best_loss': float('inf'),
            'loss_history': [],
            'gradient_history': [],
            'parameter_history': [],
            'dataset_info': {},
            'selected_parameters': {},
            'optimization_method': 'Unknown'
        }
        self.callbacks = []
        
    def start_monitoring(self, dataset_info: Dict, selected_params: Dict, method: str = 'Unknown'):
        """开始监控优化过程"""
        self.is_monitoring = True
        self.optimization_data.update({
            'start_time': datetime.now(),
            'current_iteration': 0,
            'current_loss': float('inf'),
            'best_loss': float('inf'),
            'loss_history': [],
            'gradient_history': [],
            'parameter_history': [],
            'dataset_info': dataset_info,
            'selected_parameters': selected_params,
            'optimization_method': method
        })
        
        print(f"🚀 开始监控优化过程")
        print(f"   数据集: {list(dataset_info.keys())}")
        print(f"   参数数量: {len(selected_params)}")
        print(f"   优化方法: {method}")
        
    def stop_monitoring(self):
        """停止监控"""
        self.is_monitoring = False
        print("⏹️ 停止监控优化过程")
        
    def update_progress(self, iteration: int, loss: float, 
                       gradient_norm: Optional[float] = None,
                       parameters: Optional[Dict] = None,
                       **kwargs):
        """更新优化进度"""
        if not self.is_monitoring:
            return
            
        # 更新基本信息
        self.optimization_data['current_iteration'] = iteration
        self.optimization_data['current_loss'] = loss
        
        # 更新最佳损失
        if loss < self.optimization_data['best_loss']:
            self.optimization_data['best_loss'] = loss
            
        # 记录历史数据
        self.optimization_data['loss_history'].append(loss)
        if gradient_norm is not None:
            self.optimization_data['gradient_history'].append(gradient_norm)
        if parameters is not None:
            self.optimization_data['parameter_history'].append(parameters.copy())
            
        # 调用所有回调函数
        for callback in self.callbacks:
            try:
                callback(self.optimization_data.copy())
            except Exception as e:
                print(f"⚠️ 回调函数执行失败: {e}")
                
        # 定期打印进度
        if iteration % 10 == 0:
            runtime = self.get_runtime()
            print(f"📊 迭代 {iteration}: 损失 = {loss:.6f}, 最佳 = {self.optimization_data['best_loss']:.6f}, 运行时间 = {runtime}")
            
    def add_callback(self, callback: Callable):
        """添加回调函数"""
        self.callbacks.append(callback)
        
    def get_runtime(self) -> str:
        """获取运行时间字符串"""
        if not self.optimization_data['start_time']:
            return "00:00:00"
        runtime = datetime.now() - self.optimization_data['start_time']
        hours, remainder = divmod(int(runtime.total_seconds()), 3600)
        minutes, seconds = divmod(remainder, 60)
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        
    def get_current_data(self) -> Dict:
        """获取当前监控数据"""
        data = self.optimization_data.copy()
        data['runtime'] = self.get_runtime()
        data['is_monitoring'] = self.is_monitoring
        return data

# 全局监控器实例
global_monitor = RealTimeMonitor()

def integrate_with_parameter_panel(parameter_panel):
    """与参数面板集成"""
    try:
        # 保存原始的优化启动方法
        if hasattr(parameter_panel, '_original_start_optimization'):
            return  # 已经集成过了
            
        original_start_optimization = getattr(parameter_panel, 'start_optimization', None)
        if not original_start_optimization:
            print("⚠️ 参数面板没有start_optimization方法")
            return
            
        def enhanced_start_optimization(*args, **kwargs):
            """增强的优化启动方法"""
            print("🔍 准备启动优化，收集监控信息...")
            
            # 获取选中的数据集和参数
            try:
                selected_datasets = getattr(parameter_panel.parameter_table, 'selected_datasets', set())
                selected_params = parameter_panel.parameter_table.get_parameters_for_optimization()
                
                dataset_info = {name: f"数据集_{name}" for name in selected_datasets}
                
                # 启动监控
                global_monitor.start_monitoring(
                    dataset_info=dataset_info,
                    selected_params=selected_params,
                    method="ReaxFF优化"
                )
                
                print(f"✅ 监控已启动，监控 {len(selected_params)} 个参数")
                
            except Exception as e:
                print(f"⚠️ 启动监控失败: {e}")
                
            # 调用原始的优化方法
            return original_start_optimization(*args, **kwargs)
        
        # 替换方法
        parameter_panel._original_start_optimization = original_start_optimization
        parameter_panel.start_optimization = enhanced_start_optimization
        
        print("✅ 已集成监控功能到参数面板")
        
    except Exception as e:
        print(f"❌ 集成监控功能失败: {e}")

def integrate_with_optimizer(optimizer_class):
    """与优化器类集成的装饰器"""
    
    class MonitoredOptimizer(optimizer_class):
        def __init__(self, *args, **kwargs):
            super().__init__(*args, **kwargs)
            self.monitor = global_monitor
            
        def optimize(self, *args, **kwargs):
            """重写优化方法，添加监控"""
            print("🚀 开始优化，启用实时监控...")
            
            # 如果有参数信息，启动监控
            if args and isinstance(args[0], dict):
                initial_params = args[0]
                self.monitor.start_monitoring(
                    dataset_info={"default": "优化数据集"},
                    selected_params=initial_params,
                    method=self.__class__.__name__
                )
            
            # 创建进度回调
            def progress_callback(iteration, loss, gradient_norm=None, **kwargs):
                self.monitor.update_progress(iteration, loss, gradient_norm, **kwargs)
            
            # 如果优化器支持回调，添加监控回调
            if 'callback' in kwargs:
                original_callback = kwargs['callback']
                def combined_callback(*cb_args, **cb_kwargs):
                    # 调用原始回调
                    if original_callback:
                        original_callback(*cb_args, **cb_kwargs)
                    # 调用监控回调
                    if len(cb_args) >= 2:
                        progress_callback(cb_args[0], cb_args[1], *cb_args[2:], **cb_kwargs)
                kwargs['callback'] = combined_callback
            else:
                kwargs['callback'] = progress_callback
            
            try:
                # 调用原始优化方法
                result = super().optimize(*args, **kwargs)
                self.monitor.stop_monitoring()
                return result
            except Exception as e:
                self.monitor.stop_monitoring()
                raise e
    
    return MonitoredOptimizer

def create_dashboard_callback():
    """创建仪表板回调函数"""
    def dashboard_callback(data):
        """发送数据到仪表板"""
        try:
            # 这里可以通过HTTP API或其他方式发送数据到仪表板
            # 目前只是打印到控制台
            if data['current_iteration'] % 5 == 0:  # 每5次迭代打印一次
                print(f"📊 仪表板更新: 迭代 {data['current_iteration']}, 损失 {data['current_loss']:.6f}")
        except Exception as e:
            print(f"⚠️ 仪表板回调失败: {e}")
    
    return dashboard_callback

def start_monitoring_server():
    """启动监控服务器"""
    try:
        # 尝试启动简化版仪表板
        import subprocess
        import threading
        
        def run_dashboard():
            try:
                subprocess.run([sys.executable, 'simple_dashboard.py'], 
                             capture_output=False, text=True)
            except Exception as e:
                print(f"⚠️ 启动仪表板失败: {e}")
        
        # 在后台线程中启动仪表板
        dashboard_thread = threading.Thread(target=run_dashboard)
        dashboard_thread.daemon = True
        dashboard_thread.start()
        
        # 添加仪表板回调
        global_monitor.add_callback(create_dashboard_callback())
        
        print("✅ 监控服务器已启动")
        return True
        
    except Exception as e:
        print(f"❌ 启动监控服务器失败: {e}")
        return False

def demo_real_monitoring():
    """演示真实监控功能"""
    print("🧪 演示真实监控功能")
    print("=" * 50)
    
    # 模拟真实的优化过程
    dataset_info = {"cobalt": "钴数据集"}
    selected_params = {
        "p_2_1_1": {"value": 2.15, "min": 1.7, "max": 2.6},
        "p_2_1_4": {"value": 2.15, "min": 1.7, "max": 2.6},
        "p_2_1_5": {"value": 2.15, "min": 1.7, "max": 2.6}
    }
    
    # 启动监控
    global_monitor.start_monitoring(dataset_info, selected_params, "演示优化")
    
    # 添加控制台回调
    def console_callback(data):
        if data['current_iteration'] % 10 == 0:
            print(f"📈 监控数据: 迭代 {data['current_iteration']}, 损失 {data['current_loss']:.6f}")
    
    global_monitor.add_callback(console_callback)
    
    # 模拟优化过程
    import numpy as np
    for i in range(100):
        # 模拟损失函数下降
        loss = 10.0 * np.exp(-i/30) + np.random.normal(0, 0.1)
        gradient_norm = 5.0 * np.exp(-i/20) + abs(np.random.normal(0, 0.05))
        
        # 更新监控
        global_monitor.update_progress(i, loss, gradient_norm)
        
        time.sleep(0.1)  # 模拟计算时间
    
    # 停止监控
    global_monitor.stop_monitoring()
    
    print("✅ 演示完成")

if __name__ == '__main__':
    # 启动监控服务器
    start_monitoring_server()
    
    # 运行演示
    demo_real_monitoring()
    
    print("\n💡 使用说明:")
    print("1. 在优化器中调用 global_monitor.update_progress(iteration, loss)")
    print("2. 或使用 @integrate_with_optimizer 装饰器")
    print("3. 或调用 integrate_with_parameter_panel(parameter_panel)")
    
    input("\n按回车键退出...")
