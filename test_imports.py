#!/usr/bin/env python3
"""
测试导入问题
"""

import sys
import traceback

def test_imports():
    """测试各个模块的导入"""
    print("🔍 测试模块导入...")
    
    # 测试基础导入
    try:
        import numpy as np
        print("✅ numpy 导入成功")
    except Exception as e:
        print(f"❌ numpy 导入失败: {e}")
        return False
    
    # 测试PyQt5导入
    try:
        from PyQt5.QtWidgets import QApplication, QWidget
        from PyQt5.QtCore import Qt
        print("✅ PyQt5 基础组件导入成功")
    except Exception as e:
        print(f"❌ PyQt5 导入失败: {e}")
        return False
    
    # 测试matplotlib导入
    try:
        import matplotlib
        matplotlib.use('Qt5Agg')
        import matplotlib.pyplot as plt
        print("✅ matplotlib 导入成功")
    except Exception as e:
        print(f"⚠️ matplotlib 导入失败: {e}")
    
    # 测试监控工具导入
    try:
        from monitoring_utils import start_monitoring
        print("✅ monitoring_utils 导入成功")
    except Exception as e:
        print(f"⚠️ monitoring_utils 导入失败: {e}")
    
    # 测试可视化面板导入
    try:
        from gui.visualization_panel import VisualizationPanel
        print("✅ visualization_panel 导入成功")
    except Exception as e:
        print(f"❌ visualization_panel 导入失败: {e}")
        print("详细错误:")
        traceback.print_exc()
        return False
    
    # 测试参数面板导入
    try:
        from gui.parameter_panel import ParameterPanel
        print("✅ parameter_panel 导入成功")
    except Exception as e:
        print(f"❌ parameter_panel 导入失败: {e}")
        print("详细错误:")
        traceback.print_exc()
        return False
    
    # 测试主窗口导入
    try:
        from gui.main_window import MainWindow
        print("✅ main_window 导入成功")
    except Exception as e:
        print(f"❌ main_window 导入失败: {e}")
        print("详细错误:")
        traceback.print_exc()
        return False
    
    return True

def main():
    """主函数"""
    print("🚀 ReaxFFOpt 导入测试")
    print("=" * 50)
    
    try:
        success = test_imports()
        
        if success:
            print("\n✅ 所有关键模块导入成功！")
            print("现在可以尝试运行 python main.py")
        else:
            print("\n❌ 存在导入问题，需要修复")
        
        return success
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        traceback.print_exc()
        return False

if __name__ == '__main__':
    try:
        success = main()
        input("\n按回车键退出...")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
        sys.exit(0)
