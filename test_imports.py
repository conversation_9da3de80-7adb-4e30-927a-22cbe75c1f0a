#!/usr/bin/env python3
"""
测试导入问题
"""

print("开始测试导入...")

try:
    import numpy
    print(f"✅ NumPy导入成功，版本: {numpy.__version__}")
except Exception as e:
    print(f"❌ NumPy导入失败: {e}")

try:
    import matplotlib
    print(f"✅ matplotlib导入成功，版本: {matplotlib.__version__}")
except Exception as e:
    print(f"❌ matplotlib导入失败: {e}")

try:
    from PyQt5.QtWidgets import QApplication
    print("✅ PyQt5导入成功")
except Exception as e:
    print(f"❌ PyQt5导入失败: {e}")

try:
    # 测试创建一个简单的GUI应用
    import sys
    from PyQt5.QtWidgets import QApplication, QWidget, QLabel
    
    app = QApplication(sys.argv)
    widget = QWidget()
    label = QLabel("测试", widget)
    print("✅ 简单GUI创建成功")
    app.quit()
except Exception as e:
    print(f"❌ GUI创建失败: {e}")

print("导入测试完成")
