#!/usr/bin/env python
"""
测试导入和数据处理功能
"""

import sys
import os

def test_basic_imports():
    """测试基础导入"""
    print("=== 测试基础导入 ===")
    
    try:
        print("导入numpy...")
        import numpy as np
        print("✅ numpy导入成功")
    except Exception as e:
        print(f"❌ numpy导入失败: {e}")
        return False
    
    try:
        print("导入PyQt5...")
        from PyQt5.QtWidgets import QApplication
        print("✅ PyQt5导入成功")
    except Exception as e:
        print(f"❌ PyQt5导入失败: {e}")
        return False
    
    return True

def test_data_handler():
    """测试数据处理模块"""
    print("\n=== 测试数据处理模块 ===")
    
    try:
        print("导入data_handler...")
        from data.data_handler import DatasetHandler
        print("✅ data_handler导入成功")
        
        # 测试数据集处理
        handler = DatasetHandler()
        folder_path = 'Datasets/cobalt'
        
        if os.path.exists(folder_path):
            print(f"开始处理数据集: {folder_path}")
            result = handler.process_folder(folder_path)
            print(f"处理结果: {result}")
            
            # 检查具体的结构数据
            if handler.structures:
                for name, structures in handler.structures.items():
                    print(f"数据集 {name}: {len(structures)} 个结构")
                    if structures:
                        first_struct = structures[0]
                        print(f"  第一个结构: {first_struct['description']}")
                        print(f"  原子数: {len(first_struct['atoms'])}")
                        if 'energy' in first_struct:
                            print(f"  能量: {first_struct['energy']}")
            else:
                print("⚠️  没有找到结构数据")
        else:
            print(f"❌ 数据集文件夹不存在: {folder_path}")
            
    except Exception as e:
        print(f"❌ 数据处理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

def test_optimizer():
    """测试优化器模块"""
    print("\n=== 测试优化器模块 ===")
    
    try:
        print("导入optimizer...")
        import optimizer
        print("✅ optimizer模块导入成功")
        
        # 测试可用方法
        methods = optimizer.UnifiedOptimizerFactory.get_available_methods()
        print(f"可用优化方法: {methods}")
        
        # 测试创建基础优化器
        pso = optimizer.UnifiedOptimizerFactory.create_optimizer('PSO')
        print("✅ PSO优化器创建成功")
        
    except Exception as e:
        print(f"❌ 优化器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

def main():
    """主测试函数"""
    print("开始诊断测试...")
    print(f"Python版本: {sys.version}")
    print(f"当前工作目录: {os.getcwd()}")
    print(f"Python路径: {sys.path[0]}")
    
    # 运行所有测试
    tests = [
        test_basic_imports,
        test_data_handler,
        test_optimizer
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append(False)
    
    # 总结
    print("\n=== 测试总结 ===")
    passed = sum(results)
    total = len(results)
    print(f"通过: {passed}/{total}")
    
    if all(results):
        print("🎉 所有测试通过！")
    else:
        print("⚠️  部分测试失败，请检查上述错误信息")

if __name__ == "__main__":
    main()
