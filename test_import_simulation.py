#!/usr/bin/env python3
"""
模拟数据集导入过程的测试
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data.data_handler import DatasetHandler

def simulate_import_process():
    """模拟实际的数据集导入过程"""
    print("🧪 模拟数据集导入过程")
    
    # 模拟主窗口的数据集处理器
    main_dataset_handler = None
    
    # 第一次导入 cobalt 数据集
    print("\n1. 第一次导入 cobalt 数据集")
    data_worker_handler = DatasetHandler()
    
    # 模拟处理 cobalt 数据集
    data_worker_handler.parameters['cobalt'] = {
        'p_2_1_1': {'value': 2.15, 'min': 1.7, 'max': 2.6},
        'p_2_1_4': {'value': 2.15, 'min': 1.7, 'max': 2.6},
        'p_2_1_5': {'value': 0.175, 'min': 0.05, 'max': 0.3}
    }
    data_worker_handler.dataset_paths['cobalt'] = 'Datasets/cobalt'
    
    # 模拟 dataset_import_finished 的逻辑
    if not main_dataset_handler:
        # 第一次导入，直接设置
        main_dataset_handler = data_worker_handler
        print(f"   首次设置数据集处理器引用: {type(main_dataset_handler)}")
    
    # 检查第一次导入的结果
    parameters = main_dataset_handler.get_all_parameters()
    print(f"   第一次导入后的数据集: {list(parameters.keys())}")
    print(f"   cobalt 参数数量: {len(parameters.get('cobalt', {}))}")
    
    # 第二次导入 silica 数据集
    print("\n2. 第二次导入 silica 数据集")
    new_data_worker_handler = DatasetHandler()
    
    # 模拟处理 silica 数据集
    new_data_worker_handler.parameters['silica'] = {
        'p_2_6_6': {'value': 0.53, 'min': 0.3, 'max': 0.76},
        'p_2_6_14': {'value': 3.5, 'min': -1.0, 'max': 8.0},
        'p_2_6_15': {'value': 7.25, 'min': 5.5, 'max': 9.0}
    }
    new_data_worker_handler.dataset_paths['silica'] = 'Datasets/silica'
    
    # 模拟 dataset_import_finished 的合并逻辑
    if main_dataset_handler:
        # 已有数据集处理器，合并新数据
        print("   合并新数据到现有数据集处理器")
        
        # 合并参数
        if hasattr(new_data_worker_handler, 'parameters'):
            for dataset_name, params in new_data_worker_handler.parameters.items():
                main_dataset_handler.parameters[dataset_name] = params
                print(f"     合并参数: {dataset_name} ({len(params)} 个参数)")
        
        # 合并路径
        if hasattr(new_data_worker_handler, 'dataset_paths'):
            for dataset_name, path in new_data_worker_handler.dataset_paths.items():
                main_dataset_handler.dataset_paths[dataset_name] = path
                print(f"     合并路径: {dataset_name} -> {path}")
    
    # 检查合并后的结果
    print("\n3. 验证合并结果")
    parameters = main_dataset_handler.get_all_parameters()
    print(f"   获取到的参数数据集: {list(parameters.keys())}")
    print(f"   总数据集数量: {len(parameters)}")
    
    for dataset_name, params in parameters.items():
        print(f"   {dataset_name}: {len(params)} 个参数")
    
    # 模拟构建层级结构
    print("\n4. 模拟构建层级结构")
    dataset_names = list(parameters.keys())
    print(f"   传递给 _build_dataset_hierarchy 的数据集: {dataset_names}")
    
    # 验证结果
    expected_datasets = {'cobalt', 'silica'}
    actual_datasets = set(parameters.keys())
    
    if actual_datasets == expected_datasets:
        print("\n✅ 模拟测试通过：数据集导入和合并成功")
        return True
    else:
        print(f"\n❌ 模拟测试失败：期望 {expected_datasets}，实际 {actual_datasets}")
        return False

if __name__ == "__main__":
    simulate_import_process()
