#!/usr/bin/env python3
"""
ReaxFF监控仪表板启动器
"""

import os
import sys
import time
import webbrowser
import threading
import subprocess

def open_browser_delayed():
    """延迟打开浏览器"""
    time.sleep(3)
    try:
        webbrowser.open('http://localhost:5000')
        print("🌐 已在浏览器中打开监控仪表板")
    except Exception as e:
        print(f"⚠️ 无法自动打开浏览器: {e}")
        print("📱 请手动访问: http://localhost:5000")

def main():
    """主函数"""
    print("🚀 ReaxFF优化监控仪表板启动器")
    print("=" * 50)
    
    # 检查文件
    if os.path.exists('simple_dashboard.py'):
        dashboard_file = 'simple_dashboard.py'
        print("✅ 使用简化版监控仪表板")
    elif os.path.exists('dashboard_server.py'):
        dashboard_file = 'dashboard_server.py'
        print("✅ 使用完整版监控仪表板")
    else:
        print("❌ 找不到监控仪表板文件")
        return False
    
    print(f"📊 启动文件: {dashboard_file}")
    print("🔧 正在启动服务器...")
    
    # 在后台线程中延迟打开浏览器
    browser_thread = threading.Thread(target=open_browser_delayed)
    browser_thread.daemon = True
    browser_thread.start()
    
    try:
        # 启动服务器
        result = subprocess.run([sys.executable, dashboard_file], 
                              capture_output=False, text=True)
        return result.returncode == 0
    except KeyboardInterrupt:
        print("\n👋 监控仪表板已停止")
        return True
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False

if __name__ == '__main__':
    success = main()
    if not success:
        print("\n💡 故障排除:")
        print("1. 确保已安装Flask: pip install flask")
        print("2. 确保端口5000未被占用")
        print("3. 检查防火墙设置")
    
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
