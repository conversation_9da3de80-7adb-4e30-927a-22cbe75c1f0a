"""
ReaxFFOpt 参数面板
管理力场参数的显示和编辑
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
                            QTableWidget, QTableWidgetItem, QComboBox, QCheckBox,
                            QGroupBox, QSpinBox, QDoubleSpinBox, QHeaderView, 
                            QSplitter, QTabWidget, QFormLayout, QLineEdit, QMessageBox,
                            QToolButton, QMenu)
from PyQt5.QtCore import Qt, pyqtSignal, pyqtSlot, QTimer
from PyQt5.QtGui import QColor


class ParameterTable(QTableWidget):
    """参数表格组件，用于显示和编辑力场参数"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 设置表格属性
        self.setColumnCount(6)  # 增加一列用于数据集选择
        self.setHorizontalHeaderLabels(["选择", "参数名", "当前值", "范围", "优化", "敏感性"])
        self.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)  # 选择列
        self.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeToContents)  # 参数名列
        self.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeToContents)  # 当前值列
        self.horizontalHeader().setSectionResizeMode(3, QHeaderView.Stretch)           # 范围列
        self.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeToContents)  # 优化列
        self.horizontalHeader().setSectionResizeMode(5, QHeaderView.ResizeToContents)  # 敏感性列
        self.verticalHeader().setVisible(False)
        self.setAlternatingRowColors(True)
        
        #  移除：不再添加示例数据
        # self.add_example_data()
        
        # 设置空表格提示
        self.setRowCount(0)
        self.setStyleSheet("""
            QTableWidget {
                background-color: #f8f9fa;
                alternate-background-color: #ffffff;
                selection-background-color: #007bff;
                gridline-color: #dee2e6;
            }
            QTableWidget::item {
                padding: 8px;
            }
        """)
        
        # 启用右键菜单
        self.setContextMenuPolicy(Qt.CustomContextMenu)
        self.customContextMenuRequested.connect(self.show_context_menu)
        
        # 数据集选择相关
        self.selected_datasets = set()  # 存储选中的数据集
        self.dataset_checkboxes = {}    # 存储数据集复选框
        
        # 连接单元格点击事件
        self.cellClicked.connect(self.on_cell_clicked)
    
    def show_context_menu(self, position):
        """显示右键菜单"""
        from PyQt5.QtWidgets import QMenu, QAction
        
        item = self.itemAt(position)
        if not item:
            return
        
        row = self.row(item)
        name_item = self.item(row, 1)  # 修改为第1列（参数名列）
        if not name_item:
            return
        
        # 检查是否是数据集行
        user_data = name_item.data(Qt.UserRole)
        if not user_data or not isinstance(user_data, dict):
            return
        
        item_type = user_data.get('type', 'unknown')
        
        # 只对数据集相关行显示菜单
        if item_type in ['parent', 'dataset', 'child']:
            menu = QMenu(self)
            
            # 数据集选择操作
            dataset_name = user_data.get('name', '')
            is_selected = dataset_name in self.selected_datasets
            
            select_text = " 取消选择数据集" if is_selected else " 选择数据集"
            select_action = QAction(select_text, self)
            select_action.triggered.connect(lambda: self.toggle_dataset_selection(dataset_name))
            menu.addAction(select_action)
            
            menu.addSeparator()
            
            # 删除数据集操作
            delete_action = QAction("🗑️ 删除数据集", self)
            delete_action.triggered.connect(lambda: self.delete_dataset(user_data, row))
            menu.addAction(delete_action)
            
            # 折叠/展开操作（仅对父数据集）
            if item_type == 'parent':
                is_expanded = user_data.get('expanded', True)
                toggle_text = " 折叠数据集" if is_expanded else " 展开数据集"
                toggle_action = QAction(toggle_text, self)
                toggle_action.triggered.connect(lambda: self.toggle_dataset_from_menu(user_data.get('name')))
                menu.addAction(toggle_action)
            
            # 显示数据集信息
            info_action = QAction("ℹ 数据集信息", self)
            info_action.triggered.connect(lambda: self.show_dataset_info(user_data, row))
            menu.addAction(info_action)
            
            # 显示菜单
            menu.exec_(self.mapToGlobal(position))
    
    def on_cell_clicked(self, row, column):
        """处理单元格点击事件"""
        if column == 0:  # 点击选择列
            self.toggle_row_selection(row)
    
    def toggle_row_selection(self, row):
        """切换行的选择状态"""
        try:
            name_item = self.item(row, 1)  # 参数名列
            if not name_item:
                return
            
            user_data = name_item.data(Qt.UserRole)
            if not user_data or not isinstance(user_data, dict):
                return
            
            item_type = user_data.get('type', 'unknown')
            dataset_name = user_data.get('name', '')
            
            # 只对数据集行处理选择
            if item_type in ['parent', 'dataset', 'child'] and dataset_name:
                self.toggle_dataset_selection(dataset_name)
                
        except Exception as e:
            print(f" 切换行选择状态失败: {e}")
    
    def toggle_dataset_selection(self, dataset_name):
        """切换数据集选择状态"""
        try:
            if dataset_name in self.selected_datasets:
                self.selected_datasets.remove(dataset_name)
                print(f"✅ 取消选择数据集: {dataset_name}")
                print(f"   当前选中数据集: {self.selected_datasets}")

                # 通知父窗口数据集选择变化
                self._notify_dataset_selection_changed(dataset_name, False)
            else:
                self.selected_datasets.add(dataset_name)
                print(f"✅ 选择数据集: {dataset_name}")
                print(f"   当前选中数据集: {self.selected_datasets}")

                # 通知父窗口数据集选择变化
                self._notify_dataset_selection_changed(dataset_name, True)

            # 更新复选框显示
            self.update_dataset_checkboxes()

            # 更新选择统计
            self.update_dataset_selection_stats()

        except Exception as e:
            print(f"❌ 切换数据集选择失败: {e}")
            import traceback
            traceback.print_exc()
    
    def _notify_dataset_selection_changed(self, dataset_name, is_selected):
        """通知父窗口数据集选择状态变化"""
        try:
            # 获取主窗口
            parent_window = self.parent()
            while parent_window and not hasattr(parent_window, 'dataset_handler'):
                parent_window = parent_window.parent()
            
            if parent_window and hasattr(parent_window, 'on_dataset_selection_changed'):
                parent_window.on_dataset_selection_changed(dataset_name, is_selected)
                
        except Exception as e:
            print(f" 通知数据集选择变化失败: {e}")
    
    def update_dataset_checkboxes(self):
        """更新数据集复选框显示"""
        try:
            for row in range(self.rowCount()):
                name_item = self.item(row, 1)  # 参数名列
                if not name_item:
                    continue
                
                user_data = name_item.data(Qt.UserRole)
                if not user_data or not isinstance(user_data, dict):
                    continue
                
                item_type = user_data.get('type', 'unknown')
                dataset_name = user_data.get('name', '')
                
                # 只对数据集行更新复选框
                if item_type in ['parent', 'dataset', 'child'] and dataset_name:
                    is_selected = dataset_name in self.selected_datasets
                    checkbox_item = self.item(row, 0)  # 选择列
                    
                    if not checkbox_item:
                        checkbox_item = QTableWidgetItem()
                        self.setItem(row, 0, checkbox_item)
                    
                    # 设置复选框状态
                    checkbox_item.setCheckState(Qt.Checked if is_selected else Qt.Unchecked)
                    
        except Exception as e:
            print(f" 更新数据集复选框失败: {e}")
    
    def update_dataset_selection_stats(self):
        """更新数据集选择统计"""
        try:
            total_datasets = len(self.get_all_datasets())
            selected_count = len(self.selected_datasets)
            
            # 通知父窗口更新统计信息
            parent_widget = self.parent()
            while parent_widget and not hasattr(parent_widget, 'update_dataset_selection_stats'):
                parent_widget = parent_widget.parent()
            
            if parent_widget and hasattr(parent_widget, 'update_dataset_selection_stats'):
                parent_widget.update_dataset_selection_stats(selected_count, total_datasets)
            
            print(f" 数据集选择统计: {selected_count}/{total_datasets}")
            
        except Exception as e:
            print(f" 更新数据集选择统计失败: {e}")
    
    def get_all_datasets(self):
        """获取所有数据集名称"""
        datasets = set()
        try:
            for row in range(self.rowCount()):
                name_item = self.item(row, 1)  # 参数名列
                if not name_item:
                    continue
                
                user_data = name_item.data(Qt.UserRole)
                if not user_data or not isinstance(user_data, dict):
                    continue
                
                item_type = user_data.get('type', 'unknown')
                dataset_name = user_data.get('name', '')
                
                if item_type in ['parent', 'dataset', 'child'] and dataset_name:
                    datasets.add(dataset_name)
                    
        except Exception as e:
            print(f" 获取所有数据集失败: {e}")
        
        return datasets
    
    def delete_selected_datasets(self):
        """删除选中的数据集"""
        try:
            from PyQt5.QtWidgets import QMessageBox

            if not self.selected_datasets:
                QMessageBox.information(self, "提示", "请先选择要删除的数据集")
                return

            # 确认删除
            reply = QMessageBox.question(
                self, "确认删除",
                f"确定要删除选中的 {len(self.selected_datasets)} 个数据集吗？\n\n"
                f"选中的数据集: {', '.join(self.selected_datasets)}\n\n"
                f"此操作不可撤销！",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                # 逐个删除选中的数据集
                for dataset_name in list(self.selected_datasets):
                    self.delete_single_dataset(dataset_name)
                
                print(f" 已删除 {len(self.selected_datasets)} 个数据集")
                
        except Exception as e:
            print(f" 删除选中数据集失败: {e}")
            import traceback
            traceback.print_exc()
    
    def delete_single_dataset(self, dataset_name):
        """删除单个数据集"""
        try:
            print(f"🗑️ 开始删除数据集: {dataset_name}")
            
            # 从选中列表中移除
            if dataset_name in self.selected_datasets:
                self.selected_datasets.remove(dataset_name)
            
            # 通知主窗口删除数据集
            self._notify_dataset_deleted(dataset_name)
            
            # 更新复选框显示
            self.update_dataset_checkboxes()
            
            # 更新选择统计
            self.update_dataset_selection_stats()
            
            print(f" 数据集 {dataset_name} 删除完成")
            
        except Exception as e:
            print(f" 删除数据集 {dataset_name} 失败: {e}")
            import traceback
            traceback.print_exc()
    
    def select_all_datasets(self):
        """选择所有数据集"""
        try:
            all_datasets = self.get_all_datasets()
            self.selected_datasets.update(all_datasets)
            
            # 更新复选框显示
            self.update_dataset_checkboxes()
            
            # 更新选择统计
            self.update_dataset_selection_stats()
            
            print(f" 已选择所有数据集: {len(all_datasets)} 个")
            
        except Exception as e:
            print(f" 选择所有数据集失败: {e}")
    
    def deselect_all_datasets(self):
        """取消选择所有数据集"""
        try:
            self.selected_datasets.clear()
            
            # 更新复选框显示
            self.update_dataset_checkboxes()
            
            # 更新选择统计
            self.update_dataset_selection_stats()
            
            print(f" 已取消选择所有数据集")
            
        except Exception as e:
            print(f" 取消选择所有数据集失败: {e}")
    
    def delete_dataset(self, user_data, row):
        """删除数据集（从右键菜单调用）"""
        try:
            dataset_name = user_data.get('name', '')
            if not dataset_name:
                return
            
            # 确认删除
            reply = QMessageBox.question(
                self, "确认删除", 
                f"确定要删除数据集 '{dataset_name}' 吗？\n\n此操作不可撤销！",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                self.delete_single_dataset(dataset_name)
                
        except Exception as e:
            print(f" 删除数据集失败: {e}")
            import traceback
            traceback.print_exc()
    
    def _find_dataset_rows(self, dataset_name, include_children=True):
        """查找数据集相关的所有行"""
        rows_to_delete = []
        
        for row in range(self.rowCount()):
            try:
                name_item = self.item(row, 1)  # 修改为第1列（参数名列）
                if not name_item:
                    continue
                
                user_data = name_item.data(Qt.UserRole)
                if not user_data or not isinstance(user_data, dict):
                    continue
                
                item_type = user_data.get('type', 'unknown')
                item_name = user_data.get('name', '')
                
                # 匹配父数据集行
                if item_type == 'parent' and item_name == dataset_name:
                    rows_to_delete.append(row)
                # 匹配独立数据集行
                elif item_type == 'dataset' and item_name == dataset_name:
                    rows_to_delete.append(row)
                # 如果包含子项，匹配子数据集和参数
                elif include_children and item_type in ['child', 'parameter']:
                    # 需要检查是否属于指定的父数据集
                    if self._is_child_of_dataset(row, dataset_name):
                        rows_to_delete.append(row)
                        
            except Exception as e:
                print(f" 检查第{row}行时出错: {e}")
                continue
        
        return rows_to_delete
    
    def _find_child_dataset_rows(self, child_dataset_name):
        """查找子数据集的所有行"""
        rows_to_delete = []
        found_child = False
        
        for row in range(self.rowCount()):
            try:
                name_item = self.item(row, 1)  # 修改为第1列（参数名列）
                if not name_item:
                    continue
                
                user_data = name_item.data(Qt.UserRole)
                if not user_data or not isinstance(user_data, dict):
                    continue
                
                item_type = user_data.get('type', 'unknown')
                item_name = user_data.get('name', '')
                
                # 找到子数据集行
                if item_type == 'child' and item_name == child_dataset_name:
                    rows_to_delete.append(row)
                    found_child = True
                # 找到子数据集后的参数行
                elif found_child and item_type == 'parameter':
                    rows_to_delete.append(row)
                # 遇到新的数据集或子数据集，停止查找
                elif found_child and item_type in ['parent', 'dataset', 'child']:
                    break
                    
            except Exception as e:
                print(f" 检查第{row}行时出错: {e}")
                continue
        
        return rows_to_delete
    
    def _is_child_of_dataset(self, row, parent_dataset_name):
        """检查指定行是否属于指定的父数据集"""
        # 向上查找最近的父数据集
        for check_row in range(row, -1, -1):
            try:
                check_item = self.item(check_row, 1)  # 修改为第1列（参数名列）
                if not check_item:
                    continue
                
                check_data = check_item.data(Qt.UserRole)
                if not check_data or not isinstance(check_data, dict):
                    continue
                
                check_type = check_data.get('type', 'unknown')
                check_name = check_data.get('name', '')
                
                if check_type == 'parent' and check_name == parent_dataset_name:
                    return True
                elif check_type in ['parent', 'dataset'] and check_name != parent_dataset_name:
                    return False
                    
            except Exception as e:
                continue
        
        return False
    
    def _notify_dataset_deleted(self, dataset_name):
        """通知父窗口数据集已被删除"""
        try:
            # 获取主窗口
            parent_window = self.parent()
            while parent_window and not hasattr(parent_window, 'dataset_handler'):
                parent_window = parent_window.parent()
            
            if parent_window and hasattr(parent_window, 'dataset_handler'):
                handler = parent_window.dataset_handler
                
                # 从数据处理器中移除数据集的所有相关数据
                datasets_to_remove = []
                
                # 找到要删除的数据集（包括子数据集）
                for stored_dataset in list(handler.parameters.keys()):
                    if stored_dataset == dataset_name or stored_dataset.startswith(dataset_name + '/'):
                        datasets_to_remove.append(stored_dataset)
                
                # 删除所有相关数据集的数据
                for dataset in datasets_to_remove:
                    # 删除参数
                    if dataset in handler.parameters:
                        del handler.parameters[dataset]
                        print(f" 已从数据处理器中移除参数: {dataset}")
                    
                    # 删除结构数据
                    if dataset in handler.structures:
                        del handler.structures[dataset]
                        print(f" 已从数据处理器中移除结构: {dataset}")
                    
                    # 删除能量数据
                    if dataset in handler.energies:
                        del handler.energies[dataset]
                        print(f" 已从数据处理器中移除能量: {dataset}")
                    
                    # 删除力数据
                    if dataset in handler.forces:
                        del handler.forces[dataset]
                        print(f" 已从数据处理器中移除力: {dataset}")
                    
                    # 删除训练集
                    if dataset in handler.training_sets:
                        del handler.training_sets[dataset]
                        print(f" 已从数据处理器中移除训练集: {dataset}")
                
                # 通知主窗口重新构建参数表格
                if hasattr(parent_window, 'update_parameter_table_for_selected_datasets'):
                    # 获取当前选中的数据集
                    if hasattr(parent_window, 'visualization_panel'):
                        selected_datasets = parent_window.visualization_panel.get_selected_datasets()
                        print(f" 数据集删除后，重新构建参数表格，选中数据集: {selected_datasets}")
                        parent_window.update_parameter_table_for_selected_datasets(selected_datasets)
                    else:
                        # 如果没有可视化面板，重建所有参数表格
                        if hasattr(parent_window, '_rebuild_parameter_table'):
                            parent_window._rebuild_parameter_table()
                            print(" 数据集删除后，重新构建参数表格")
                
                # 更新可视化面板
                if hasattr(parent_window, 'visualization_panel'):
                    if hasattr(parent_window.visualization_panel, 'update_dataset_selector'):
                        parent_window.visualization_panel.update_dataset_selector()
                        print(" 数据集删除后，更新可视化面板数据集选择器")
                    
                    # 删除路径信息
                    if dataset in handler.dataset_paths:
                        del handler.dataset_paths[dataset]
                        print(f" 已从数据处理器中移除路径信息: {dataset}")
                    
                    # 删除其他类型的数据
                    for attr_name in ['control_files', 'json_files', 'csv_files', 'yaml_files', 'data_files']:
                        if hasattr(handler, attr_name) and dataset in getattr(handler, attr_name):
                            del getattr(handler, attr_name)[dataset]
                            print(f" 已从数据处理器中移除{attr_name}: {dataset}")
                
                print(f" 总共删除了 {len(datasets_to_remove)} 个数据集的数据")
                
                # 只删除特定数据集，不清空整个表格
                print(f" 删除特定数据集: {dataset_name}")
                
                #  重要修复：清理可视化面板中的数据
                if hasattr(parent_window, 'visualization_panel'):
                    viz_panel = parent_window.visualization_panel
                    
                    # 清理多数据集历史数据
                    if hasattr(viz_panel, 'multi_dataset_history'):
                        datasets_to_remove_from_viz = []
                        for stored_dataset in list(viz_panel.multi_dataset_history.keys()):
                            if stored_dataset == dataset_name or stored_dataset.startswith(dataset_name + '/'):
                                datasets_to_remove_from_viz.append(stored_dataset)
                        
                        for dataset in datasets_to_remove_from_viz:
                            del viz_panel.multi_dataset_history[dataset]
                            print(f" 已从可视化面板中移除数据集历史: {dataset}")
                    
                    # 清理当前数据集列表
                    if hasattr(viz_panel, 'current_datasets'):
                        datasets_to_remove_from_current = []
                        for stored_dataset in list(viz_panel.current_datasets):
                            if stored_dataset == dataset_name or stored_dataset.startswith(dataset_name + '/'):
                                datasets_to_remove_from_current.append(stored_dataset)
                        
                        for dataset in datasets_to_remove_from_current:
                            viz_panel.current_datasets.remove(dataset)
                            print(f" 已从当前数据集列表中移除: {dataset}")
                        
                        # 保留其他数据集，不清空整个列表
                        print(f" 当前数据集列表剩余: {len(viz_panel.current_datasets)} 个数据集")
                    
                    # 清空优化历史
                    if hasattr(viz_panel, 'optimization_history'):
                        viz_panel.optimization_history = {
                            'iterations': [],
                            'total_loss': [],
                            'train_loss': [],
                            'val_loss': [],
                            'search_path': []
                        }
                        print(" 已清空优化历史")
                    
                    # 更新数据集选择器
                    if hasattr(viz_panel, 'update_dataset_selector'):
                        try:
                            viz_panel.update_dataset_selector()
                            print(f" 已更新数据集选择器")
                        except Exception as e:
                            print(f" 更新数据集选择器时出错: {e}")
                    
                    # 清空所有图表
                    if hasattr(viz_panel, 'plot_optimization_curves'):
                        try:
                            viz_panel.plot_optimization_curves()
                            print(f" 已重新绘制优化曲线")
                        except Exception as e:
                            print(f" 重新绘制图表时出错: {e}")
                    
                    if hasattr(viz_panel, 'plot_parameter_space'):
                        try:
                            viz_panel.plot_parameter_space()
                            print(f" 已重新绘制参数空间")
                        except Exception as e:
                            print(f" 重新绘制参数空间时出错: {e}")
                    
                    if hasattr(viz_panel, 'plot_sensitivity_analysis'):
                        try:
                            # 清空敏感性分析
                            viz_panel.sensitivity_data = {'parameters': [], 'sensitivities': []}
                            viz_panel.plot_sensitivity_analysis([], [])
                            print(f" 已清空敏感性分析")
                        except Exception as e:
                            print(f" 清空敏感性分析时出错: {e}")
                    
                    # 更新数据集信息标签
                    if hasattr(viz_panel, 'update_dataset_info_label'):
                        try:
                            viz_panel.update_dataset_info_label()
                            print(f" 已更新数据集信息标签")
                        except Exception as e:
                            print(f" 更新数据集信息标签时出错: {e}")
                
                #  重要修复：清理主窗口的数据处理器引用
                if len(handler.parameters) == 0:
                    print(" 所有数据集已删除，清理数据处理器引用")
                    parent_window.dataset_handler = None
                
                # 更新参数选择统计
                if hasattr(parent_window, 'parameter_panel'):
                    if hasattr(parent_window.parameter_panel, 'update_selection_stats'):
                        parent_window.parameter_panel.update_selection_stats()
                        print(" 已更新参数选择统计")
                    
        except Exception as e:
            print(f" 通知数据集删除时出错: {e}")
            import traceback
            traceback.print_exc()
    
    def toggle_dataset_from_menu(self, dataset_name):
        """从菜单触发折叠/展开"""
        try:
            parent_window = self.parent()
            while parent_window and not hasattr(parent_window, '_toggle_dataset_collapse'):
                parent_window = parent_window.parent()
            
            if parent_window:
                parent_window._toggle_dataset_collapse(dataset_name)
        except Exception as e:
            print(f" 切换折叠状态时出错: {e}")
    
    def show_dataset_info(self, user_data, row):
        """显示数据集信息"""
        from PyQt5.QtWidgets import QMessageBox
        
        dataset_name = user_data.get('name', '未知数据集')
        item_type = user_data.get('type', 'unknown')
        
        # 收集数据集信息
        info_text = f"数据集名称: {dataset_name}\n"
        info_text += f"类型: {item_type}\n"
        
        # 统计参数数量
        param_count = 0
        for check_row in range(self.rowCount()):
            try:
                check_item = self.item(check_row, 0)
                if check_item:
                    check_data = check_item.data(Qt.UserRole)
                    if (check_data and isinstance(check_data, dict) and 
                        check_data.get('type') == 'parameter' and
                        self._is_child_of_dataset(check_row, dataset_name)):
                        param_count += 1
            except:
                continue
        
        info_text += f"参数数量: {param_count}\n"
        info_text += f"表格行位置: {row + 1}"
        
        QMessageBox.information(self, f"数据集信息 - {dataset_name}", info_text)
    
    @staticmethod
    def parse_parameter_range(range_text, current_value):
        """
        智能解析参数范围文本，支持各种格式
        
        Args:
            range_text (str): 范围文本，如 "1.0 - 2.0", "-0.5 - -0.3", "0.1-0.9"
            current_value (float): 当前参数值，用于生成默认范围
            
        Returns:
            tuple: (min_val, max_val)
        """
        range_text = range_text.strip()
        
        # 如果范围文本为空或只有空格，使用默认范围
        if not range_text:
            return current_value * 0.8, current_value * 1.2
        
        try:
            # 尝试多种分隔符
            separators = [' - ', '-', ' to ', '~', ',']
            
            for sep in separators:
                if sep in range_text:
                    parts = range_text.split(sep, 1)  # 只分割一次
                    if len(parts) == 2:
                        try:
                            min_val = float(parts[0].strip())
                            max_val = float(parts[1].strip())
                            
                            # 确保min <= max
                            if min_val > max_val:
                                min_val, max_val = max_val, min_val
                            
                            # 如果min和max相同，创建一个小的范围
                            if abs(min_val - max_val) < 1e-10:
                                if min_val == 0:
                                    min_val, max_val = -0.1, 0.1
                                else:
                                    delta = abs(min_val) * 0.1
                                    min_val = min_val - delta
                                    max_val = min_val + 2 * delta
                            
                            return min_val, max_val
                        except ValueError:
                            continue
            
            # 如果没有找到分隔符，尝试解析为单个数值
            try:
                single_val = float(range_text)
                # 为单个值创建范围
                if single_val == 0:
                    return -0.1, 0.1
                else:
                    delta = abs(single_val) * 0.2
                    return single_val - delta, single_val + delta
            except ValueError:
                pass
                
        except Exception:
            pass
        
        # 所有解析都失败，使用基于当前值的默认范围
        if current_value == 0:
            return -0.1, 0.1
        elif current_value > 0:
            return current_value * 0.5, current_value * 1.5
        else:
            return current_value * 1.5, current_value * 0.5
    
    def add_example_data(self):
        """添加示例参数数据"""
        test_data = [
            ("p_val1", 1.2345, "0.8 - 1.5", True, 0.0),
            ("p_val2", 0.7654, "0.5 - 1.0", True, 0.0),
            ("p_bond1", 85.32, "80.0 - 90.0", False, 0.0),
            ("p_angle1", 2.456, "2.0 - 3.0", True, 0.0),
            ("p_tors1", 10.75, "5.0 - 15.0", False, 0.0),
            ("p_hbond1", 0.123, "0.05 - 0.2", True, 0.0),
        ]
        
        self.setRowCount(len(test_data))
        
        for row, (name, value, range_val, optimize, sensitivity) in enumerate(test_data):
            # 参数名
            self.setItem(row, 0, QTableWidgetItem(name))
            
            # 当前值
            value_item = QTableWidgetItem(f"{value:.4f}")
            value_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.setItem(row, 1, value_item)
            
            # 范围
            range_item = QTableWidgetItem(range_val)
            range_item.setTextAlignment(Qt.AlignCenter)
            self.setItem(row, 2, range_item)
            
            # 优化复选框
            checkbox = QCheckBox()
            checkbox.setChecked(optimize)
            checkbox_widget = QWidget()
            checkbox_layout = QHBoxLayout(checkbox_widget)
            checkbox_layout.addWidget(checkbox)
            checkbox_layout.setAlignment(Qt.AlignCenter)
            checkbox_layout.setContentsMargins(0, 0, 0, 0)
            self.setCellWidget(row, 3, checkbox_widget)
            
            # 敏感性（初始为0）
            sensitivity_item = QTableWidgetItem(f"{sensitivity:.2f}")
            sensitivity_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.setItem(row, 4, sensitivity_item)
    
    def update_sensitivities(self, sensitivities):
        """更新参数敏感性值
        
        Args:
            sensitivities: 参数名称到敏感性值的字典
        """
        for row in range(self.rowCount()):
            param_name = self.item(row, 0).text()
            if param_name in sensitivities:
                # 更新敏感性值
                sensitivity = sensitivities[param_name]
                sensitivity_item = QTableWidgetItem(f"{sensitivity:.2f}")
                sensitivity_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                
                # 根据敏感性值设置背景色
                if sensitivity > 0.7:
                    sensitivity_item.setBackground(QColor(255, 200, 200))  # 高敏感性：浅红色
                elif sensitivity > 0.3:
                    sensitivity_item.setBackground(QColor(255, 255, 200))  # 中敏感性：浅黄色
                else:
                    sensitivity_item.setBackground(QColor(200, 255, 200))  # 低敏感性：浅绿色
                
                self.setItem(row, 4, sensitivity_item)
    
    def get_parameters_for_optimization(self):
        """获取用于优化的参数 - 修复版本，基于选中的数据集"""
        selected_params = {}

        # 首先检查是否有选中的数据集
        if not self.selected_datasets:
            print(" 没有选中任何数据集，无法获取参数")
            return selected_params

        print(f" 基于选中数据集获取参数: {list(self.selected_datasets)}")

        for row in range(self.rowCount()):
            try:
                # 检查是否为参数行（而不是标题行）
                name_item = self.item(row, 0)
                if not name_item:
                    continue

                # 通过UserRole数据判断行类型
                user_data = name_item.data(Qt.UserRole)
                if not user_data or user_data.get('type') != 'parameter':
                    # 跳过非参数行（标题行、数据集行等）
                    continue

                # 检查参数是否属于选中的数据集
                param_dataset = user_data.get('dataset', '')
                if param_dataset not in self.selected_datasets:
                    continue

                # 检查复选框是否选中
                checkbox_widget = self.cellWidget(row, 3)
                if not checkbox_widget:
                    continue

                checkbox = checkbox_widget.findChild(QCheckBox)
                if not checkbox or not checkbox.isChecked():
                    continue

                # 获取参数信息 - 使用UserRole数据而不是文本解析
                param_name = user_data.get('name', '')
                if not param_name:
                    # 回退到文本解析
                    text = name_item.text()
                    if '└─' in text:
                        param_name = text.split('└─')[-1].strip()
                    else:
                        param_name = text.strip()

                # 获取参数值
                value_item = self.item(row, 1)
                if not value_item:
                    continue

                try:
                    value = float(value_item.text())
                except (ValueError, TypeError):
                    continue

                # 获取参数范围
                range_item = self.item(row, 2)
                range_text = range_item.text() if range_item else ""

                # 解析范围
                min_val = value * 0.5  # 默认范围
                max_val = value * 1.5

                if ' - ' in range_text:
                    try:
                        parts = range_text.split(' - ')
                        if len(parts) >= 2:
                            min_val = float(parts[0].strip())
                            max_val = float(parts[1].strip())
                    except (ValueError, IndexError):
                        pass  # 使用默认范围

                # 使用数据集名称作为前缀，确保参数名唯一
                full_param_name = f"{param_dataset}_{param_name}"

                selected_params[full_param_name] = {
                    'value': value,
                    'min': min_val,
                    'max': max_val,
                    'row': row,
                    'dataset': param_dataset,
                    'original_name': param_name
                }

            except Exception as e:
                print(f" 跳过无效参数行 {row}: {e}")
                continue

        print(f" 选择了 {len(selected_params)} 个参数进行优化")
        return selected_params

    def get_selected_parameters(self):
        """获取选中的参数 - 兼容性方法"""
        return self.get_parameters_for_optimization()

    def select_all_parameters(self):
        """全选所有参数 - 超级安全版本，100%避免错误"""
        count = 0
        total_rows = self.rowCount()
        
        print(f"🔍 开始全选操作，总行数: {total_rows}")
        
        if total_rows == 0:
            print("   表格为空，无需操作")
            return
        
        for row in range(total_rows):
            try:
                # 多重安全检查
                if not self._is_valid_row(row):
                    continue
                
                if not self._is_parameter_row(row):
                    continue
                
                if not self._has_valid_checkbox(row):
                    continue
                
                # 安全操作复选框
                success = self._safe_set_checkbox(row, True)
                if success:
                    count += 1
                
            except Exception as e:
                print(f"    处理第{row}行时发生未知错误: {e}")
                continue
        
        print(f" 全选完成: 成功操作 {count} 个参数")

    def deselect_all_parameters(self):
        """取消全选所有参数 - 超级安全版本，100%避免错误"""
        count = 0
        total_rows = self.rowCount()
        
        print(f" 开始取消全选操作，总行数: {total_rows}")
        
        if total_rows == 0:
            print("   表格为空，无需操作")
            return
        
        for row in range(total_rows):
            try:
                # 多重安全检查
                if not self._is_valid_row(row):
                    continue
                
                if not self._is_parameter_row(row):
                    continue
                
                if not self._has_valid_checkbox(row):
                    continue
                
                # 安全操作复选框
                success = self._safe_set_checkbox(row, False)
                if success:
                    count += 1
                
            except Exception as e:
                print(f"    处理第{row}行时发生未知错误: {e}")
                continue
        
        print(f" 取消全选完成: 成功操作 {count} 个参数")
    
    def _is_valid_row(self, row):
        """检查行是否有效"""
        try:
            if row < 0 or row >= self.rowCount():
                print(f"   跳过第{row}行: 行索引无效")
                return False
            return True
        except:
            return False
    
    def _is_parameter_row(self, row):
        """检查指定行是否是参数行"""
        try:
            name_item = self.item(row, 1)  # 修改为第1列（参数名列）
            if not name_item:
                return False
            
            user_data = name_item.data(Qt.UserRole)
            if not user_data or not isinstance(user_data, dict):
                return False
            
            return user_data.get('type') == 'parameter'
            
        except Exception as e:
            print(f" 检查参数行失败: {e}")
            return False
    
    def _has_valid_checkbox(self, row):
        """检查指定行是否有有效的复选框"""
        try:
            # 检查优化列（第5列）
            checkbox_item = self.item(row, 4)  # 优化列
            if checkbox_item and checkbox_item.flags() & Qt.ItemIsUserCheckable:
                return True
            
            return False
            
        except Exception as e:
            print(f" 检查复选框失败: {e}")
            return False
    
    def _safe_set_checkbox(self, row, checked, invert=False):
        """安全设置复选框状态
        
        Args:
            row (int): 行号
            checked (bool): 是否选中，None表示使用invert模式
            invert (bool): 是否反转当前状态
        """
        try:
            checkbox_widget = self.cellWidget(row, 3)
            if not checkbox_widget:
                return False
                
            checkbox = checkbox_widget.findChild(QCheckBox)
            if not checkbox:
                return False
            
            if invert:
                # 反转当前状态
                checkbox.setChecked(not checkbox.isChecked())
            elif checked is not None:
                # 设置指定状态
                checkbox.setChecked(checked)
            else:
                return False
                
            return True
            
        except Exception as e:
            print(f" 设置复选框状态失败，行{row}: {e}")
            return False

    def invert_parameter_selection(self):
        """反选参数"""
        print(" 开始反选操作，总行数:", self.rowCount())
        count = 0
        
        for row in range(self.rowCount()):
            try:
                # 检查是否是参数行
                if not self._is_parameter_row(row):
                    continue
                
                # 检查是否有有效的复选框
                if not self._has_valid_checkbox(row):
                    continue
                
                # 切换复选框状态
                success = self._safe_set_checkbox(row, None, invert=True)
                if success:
                    count += 1
                    
            except Exception as e:
                print(f"    反选第{row}行时出错: {e}")
                continue
        
        print(f" 反选完成: 成功操作 {count} 个参数")
        
        # 通知父窗口更新统计
        if hasattr(self.parent(), 'update_selection_stats'):
            self.parent().update_selection_stats()
    
    def update_selection_stats(self):
        """更新选择统计 - 兼容性方法"""
        if hasattr(self.parent(), 'update_selection_stats'):
            self.parent().update_selection_stats()
    
    def start_optimization(self):
        """开始优化"""
        # 获取选中的参数
        try:
            selected_params = self.get_parameters_for_optimization()
            if not selected_params:
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.warning(self, "参数错误", "请先选择要优化的参数")
                return
            
            # 获取优化配置
            config = self.parent().config_panel.get_config()
            config['parameters'] = selected_params
            
            # 发射信号给主窗口
            self.parent().optimization_started.emit(config)
            
            # 更新按钮状态
            self.parent().start_button.setEnabled(False)
            self.parent().stop_button.setEnabled(True)
            
            print(f" 开始优化，选中 {len(selected_params)} 个参数")
            
        except Exception as e:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(self, "优化启动失败", f"启动优化时发生错误：\n{str(e)}")
            print(f" 优化启动失败: {e}")
    
    def get_selected_parameters(self):
        """获取选中的参数 - 兼容性方法"""
        return self.get_parameters_for_optimization()


class OptimizationConfigPanel(QWidget):
    """优化配置面板"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
    
    def setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        
        # 优化方法选择
        method_group = QGroupBox("优化方法")
        method_layout = QFormLayout(method_group)
        
        from PyQt5.QtWidgets import QComboBox, QSpinBox, QDoubleSpinBox, QCheckBox
        
        self.method_combo = QComboBox()
        self.method_combo.addItems([
            "遗传算法 (GA)",
            "粒子群优化 (PSO)", 
            "模拟退火 (SA)",
            "差分进化 (DE)",
            "自适应多尺度优化",
            "贝叶斯优化",
            "强化学习优化"
        ])
        method_layout.addRow("优化算法:", self.method_combo)
        
        self.population_spin = QSpinBox()
        self.population_spin.setRange(10, 1000)
        self.population_spin.setValue(50)
        method_layout.addRow("种群大小:", self.population_spin)
        
        self.iterations_spin = QSpinBox()
        self.iterations_spin.setRange(10, 10000)
        self.iterations_spin.setValue(200)
        method_layout.addRow("最大迭代数:", self.iterations_spin)
        
        self.tolerance_spin = QDoubleSpinBox()
        self.tolerance_spin.setRange(1e-10, 1e-2)
        self.tolerance_spin.setValue(1e-5)
        self.tolerance_spin.setDecimals(8)
        method_layout.addRow("收敛容忍度:", self.tolerance_spin)
        
        layout.addWidget(method_group)
        
        # 高级选项
        advanced_group = QGroupBox("高级选项")
        advanced_layout = QFormLayout(advanced_group)
        
        self.use_quantum_check = QCheckBox("使用量子加速")
        advanced_layout.addRow("", self.use_quantum_check)
        
        self.use_ai_check = QCheckBox("使用AI辅助")
        advanced_layout.addRow("", self.use_ai_check)
        
        self.parallel_spin = QSpinBox()
        self.parallel_spin.setRange(1, 32)
        self.parallel_spin.setValue(4)
        advanced_layout.addRow("并行线程数:", self.parallel_spin)
        
        layout.addWidget(advanced_group)
        layout.addStretch()
    
    def get_config(self):
        """获取配置"""
        return {
            'method': self.method_combo.currentText(),
            'population_size': self.population_spin.value(),
            'max_iterations': self.iterations_spin.value(),
            'tolerance': self.tolerance_spin.value(),
            'use_quantum': self.use_quantum_check.isChecked(),
            'use_ai_model': self.use_ai_check.isChecked(),
            'parallel_threads': self.parallel_spin.value()
        }


class ParameterPanel(QWidget):
    """参数面板 - 用于参数选择和优化控制"""
    
    # 定义信号
    optimization_started = pyqtSignal(dict)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        
        # ✅ 先创建参数表格
        self.parameter_table = ParameterTable()
        
        # ✅ 然后创建控制面板（可以安全引用parameter_table）
        control_panel = QWidget()
        control_layout = QHBoxLayout(control_panel)
        
        # 参数选择功能已集成到数据集选择按钮中，无需重复的参数选择组

        # 数据集选择按钮组
        dataset_group = QGroupBox("数据集选择")
        dataset_layout = QHBoxLayout(dataset_group)

        # 数据集全选按钮
        self.dataset_select_all_btn = QPushButton("全选数据集")
        self.dataset_select_all_btn.clicked.connect(self.select_all_datasets)
        self.dataset_select_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 3px;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)

        # 数据集清空选择按钮
        self.dataset_clear_btn = QPushButton("清空选择")
        self.dataset_clear_btn.clicked.connect(self.deselect_all_datasets)
        self.dataset_clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 3px;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)

        # 删除选中数据集按钮
        self.dataset_delete_btn = QPushButton("删除选中")
        self.dataset_delete_btn.clicked.connect(self.delete_selected_datasets)
        self.dataset_delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #e67e22;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 3px;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #d35400;
            }
        """)

        # 新增数据集按钮
        self.dataset_add_btn = QPushButton("+ 新增数据集")
        self.dataset_add_btn.clicked.connect(self.add_new_dataset)
        self.dataset_add_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 3px;
                font-size: 11px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)

        dataset_layout.addWidget(self.dataset_add_btn)
        dataset_layout.addWidget(self.dataset_select_all_btn)
        dataset_layout.addWidget(self.dataset_clear_btn)
        dataset_layout.addWidget(self.dataset_delete_btn)
        dataset_layout.addStretch()

        # 添加数据集选择组到控制布局
        control_layout.addWidget(dataset_group)

        # 数据集选择统计标签
        self.dataset_selection_stats_label = QLabel("数据集: 0/0 个")
        dataset_layout.addWidget(self.dataset_selection_stats_label)
        

        
        # 优化控制组
        optimization_group = QGroupBox("优化控制")
        optimization_layout = QHBoxLayout(optimization_group)
        
        self.start_button = QPushButton("开始优化")
        self.start_button.setObjectName("start_button")
        self.start_button.clicked.connect(self.parameter_table.start_optimization)
        optimization_layout.addWidget(self.start_button)
        
        self.stop_button = QPushButton("停止优化")
        self.stop_button.setObjectName("stop_button")
        self.stop_button.setEnabled(False)
        self.stop_button.clicked.connect(self.stop_optimization)
        optimization_layout.addWidget(self.stop_button)
        
        optimization_layout.addStretch()
        control_layout.addWidget(optimization_group)
        
        main_layout.addWidget(control_panel)
        
        # 添加参数表格到布局
        main_layout.addWidget(self.parameter_table)
        
        # 配置面板
        self.config_panel = OptimizationConfigPanel()
        main_layout.addWidget(self.config_panel)
        
        # 连接信号
        self.parameter_table.itemChanged.connect(self.update_selection_stats)
        self.parameter_table.cellChanged.connect(self.update_selection_stats)
        
        # 定时更新统计
        self.stats_timer = QTimer()
        self.stats_timer.timeout.connect(self.update_selection_stats)
        self.stats_timer.start(2000)  # 每2秒更新一次
        
        # 不再自动添加示例数据，保持表格为空直到用户导入数据
    
    def show_dataset_selection_menu(self):
        """显示数据集选择菜单 - 已简化为直接选择所有数据集"""
        # 直接调用选择所有数据集
        self.parameter_table.select_all_datasets()
    
    def get_available_datasets(self):
        """获取可用的数据集列表"""
        datasets = set()
        
        print(f" 扫描参数表格，总行数: {self.parameter_table.rowCount()}")
        
        for row in range(self.parameter_table.rowCount()):
            try:
                item = self.parameter_table.item(row, 1)  # 修改为第1列（参数名列）
                if not item:
                    continue
                
                user_data = item.data(Qt.UserRole)
                if user_data and isinstance(user_data, dict):
                    item_type = user_data.get('type')
                    item_name = user_data.get('name')
                    
                    print(f"   行 {row}: 类型={item_type}, 名称={item_name}")
                    
                    if item_type in ['parent', 'dataset', 'child']:
                        if item_name:
                            datasets.add(item_name)
                            print(f"    添加数据集: {item_name}")
            except Exception as e:
                print(f"   处理第{row}行时出错: {e}")
                continue
        
        result = sorted(list(datasets))
        print(f"🎯 最终找到的数据集: {result}")
        return result
    
    def count_dataset_parameters(self, dataset_name):
        """统计指定数据集的参数数量"""
        count = 0
        in_dataset = False
        
        for row in range(self.parameter_table.rowCount()):
            try:
                item = self.parameter_table.item(row, 1)  # 修改为第1列（参数名列）
                if not item:
                    continue
                
                user_data = item.data(Qt.UserRole)
                if user_data and isinstance(user_data, dict):
                    item_type = user_data.get('type')
                    item_name = user_data.get('name')
                    
                    # 找到数据集开始
                    if item_type in ['parent', 'dataset', 'child'] and item_name == dataset_name:
                        in_dataset = True
                        continue
                    
                    # 遇到新的数据集，停止计数
                    elif item_type in ['parent', 'dataset', 'child'] and item_name != dataset_name:
                        if in_dataset:
                            break
                    
                    # 在数据集内的参数
                    elif in_dataset and item_type == 'parameter':
                        count += 1
            except:
                continue
        
        return count
    
    def select_dataset_parameters(self, dataset_name):
        """选择指定数据集的所有参数"""
        count = 0
        in_dataset = False
        
        print(f" 选择数据集 '{dataset_name}' 的所有参数")
        
        for row in range(self.parameter_table.rowCount()):
            try:
                item = self.parameter_table.item(row, 1)  # 修改为第1列（参数名列）
                if not item:
                    continue
                
                user_data = item.data(Qt.UserRole)
                if user_data and isinstance(user_data, dict):
                    item_type = user_data.get('type')
                    item_name = user_data.get('name')
                    
                    # 找到数据集开始
                    if item_type in ['parent', 'dataset', 'child'] and item_name == dataset_name:
                        in_dataset = True
                        continue
                    
                    # 遇到新的数据集，停止选择
                    elif item_type in ['parent', 'dataset', 'child'] and item_name != dataset_name:
                        if in_dataset:
                            break
                    
                    # 在数据集内的参数
                    elif in_dataset and item_type == 'parameter':
                        # 选择参数（修改为第5列，优化列）
                        checkbox_item = self.parameter_table.item(row, 4)  # 优化列
                        if checkbox_item:
                            checkbox_item.setCheckState(Qt.Checked)
                            count += 1
            except Exception as e:
                print(f" 选择参数时出错: {e}")
                continue
        
        print(f" 已选择 {count} 个参数")
        self.update_selection_stats()
    

    
    def select_visible_parameters(self):
        """仅选择当前可见的参数"""
        count = 0
        
        for row in range(self.parameter_table.rowCount()):
            try:
                # 检查行是否隐藏
                if self.parameter_table.isRowHidden(row):
                    continue
                
                if not self.parameter_table._is_parameter_row(row):
                    continue
                
                if not self.parameter_table._has_valid_checkbox(row):
                    continue
                
                success = self.parameter_table._safe_set_checkbox(row, True)
                if success:
                    count += 1
                    
            except Exception as e:
                continue
        
        print(f" 已选择 {count} 个可见参数")
        self.update_selection_stats()
    
    def update_selection_stats(self):
        """更新选择统计"""
        try:
            total_params = 0
            selected_params = 0
            
            for row in range(self.parameter_table.rowCount()):
                if self.parameter_table._is_parameter_row(row):
                    total_params += 1
                    if self.parameter_table._has_valid_checkbox(row):
                        checkbox_item = self.parameter_table.item(row, 4)  # 优化列
                        if checkbox_item and checkbox_item.checkState() == Qt.Checked:
                            selected_params += 1
            
            self.selection_stats_label.setText(f"已选择: {selected_params}/{total_params} 个参数")
            
        except Exception as e:
            print(f" 更新选择统计失败: {e}")
    
    def update_dataset_selection_stats(self, selected_count, total_count):
        """更新数据集选择统计"""
        try:
            self.dataset_selection_stats_label.setText(f"数据集: {selected_count}/{total_count} 个")
        except Exception as e:
            print(f" 更新数据集选择统计失败: {e}")
    
    def select_all_datasets(self):
        """选择所有数据集"""
        try:
            self.parameter_table.select_all_datasets()
        except Exception as e:
            print(f" 选择所有数据集失败: {e}")

    def deselect_all_datasets(self):
        """取消选择所有数据集"""
        try:
            self.parameter_table.deselect_all_datasets()
        except Exception as e:
            print(f" 取消选择所有数据集失败: {e}")
    
    def delete_selected_datasets(self):
        """删除选中的数据集"""
        try:
            self.parameter_table.delete_selected_datasets()
        except Exception as e:
            print(f" 删除选中数据集失败: {e}")

    def add_new_dataset(self):
        """新增数据集"""
        try:
            from PyQt5.QtWidgets import QFileDialog, QMessageBox

            # 弹出文件夹选择对话框
            folder_path = QFileDialog.getExistingDirectory(
                self,
                "选择数据集文件夹",
                "",  # 默认路径
                QFileDialog.ShowDirsOnly | QFileDialog.DontResolveSymlinks
            )

            if folder_path:
                print(f"📁 用户选择了数据集文件夹: {folder_path}")

                # 通知主窗口导入数据集
                self._notify_import_dataset(folder_path)

                QMessageBox.information(
                    self, "导入成功",
                    f"数据集文件夹已导入：\n{folder_path}\n\n"
                    "请在参数表格中查看新导入的数据集。"
                )
            else:
                print("❌ 用户取消了数据集文件夹选择")

        except Exception as e:
            print(f"❌ 新增数据集失败: {e}")
            import traceback
            traceback.print_exc()

            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(
                self, "导入失败",
                f"导入数据集时发生错误：\n{str(e)}\n\n"
                "请检查文件夹是否包含有效的数据集文件。"
            )

    def _notify_import_dataset(self, folder_path):
        """通知主窗口导入数据集"""
        try:
            # 获取主窗口引用
            main_window = None
            parent = self.parent()
            while parent:
                if hasattr(parent, 'import_dataset_folder_from_path'):
                    main_window = parent
                    break
                parent = parent.parent()

            if main_window:
                print(f"📤 通知主窗口导入数据集: {folder_path}")
                main_window.import_dataset_folder_from_path(folder_path)
            else:
                print("❌ 无法找到主窗口引用")

        except Exception as e:
            print(f"❌ 通知主窗口导入数据集失败: {e}")
            import traceback
            traceback.print_exc()
    
    def select_sensitive_parameters(self):
        """根据敏感性选择参数"""
        # 选择敏感度大于0.3的参数
        for row in range(self.parameter_table.rowCount()):
            sensitivity = float(self.parameter_table.item(row, 4).text())
            checkbox_widget = self.parameter_table.cellWidget(row, 3)
            checkbox = checkbox_widget.findChild(QCheckBox)
            
            if sensitivity > 0.3:  # 选择中高敏感度参数
                checkbox.setChecked(True)
            else:
                checkbox.setChecked(False)
    
    def set_parameter_sensitivities(self, sensitivities):
        """设置参数敏感性值
        
        Args:
            sensitivities: 参数名称到敏感性值的字典
        """
        self.parameter_table.update_sensitivities(sensitivities)
    
    def stop_optimization(self):
        """停止优化过程"""
        # 更新按钮状态
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
    
    def get_selected_parameters(self):
        """获取选中的参数
        
        Returns:
            dict: 选中的参数字典 {参数名: 参数值}
        """
        selected_params = {}
        
        for row in range(self.parameter_table.rowCount()):
            try:
                # 检查是否是参数行
                if not self.parameter_table._is_parameter_row(row):
                    continue
                
                # 检查是否被选中
                if not self.parameter_table._has_valid_checkbox(row):
                    continue
                
                checkbox_widget = self.parameter_table.cellWidget(row, 3)
                if not checkbox_widget:
                    continue
                
                checkbox = checkbox_widget.findChild(QCheckBox)
                if not checkbox or not checkbox.isChecked():
                    continue
                
                # 获取参数名和值
                name_item = self.parameter_table.item(row, 0)
                value_item = self.parameter_table.item(row, 1)
                
                if name_item and value_item:
                    # 从显示名称中提取实际参数名
                    param_name = name_item.text()
                    if '└─' in param_name:
                        param_name = param_name.split('└─')[-1].strip()
                    
                    try:
                        param_value = float(value_item.text())
                        selected_params[param_name] = param_value
                    except ValueError:
                        continue
                        
            except Exception as e:
                print(f" 获取第{row}行参数时出错: {e}")
                continue
        
        print(f" 获取到选中的参数: {len(selected_params)} 个")
        return selected_params 