#!/usr/bin/env python3
"""
修复NumPy版本冲突问题
"""

import subprocess
import sys

def run_command(command):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def check_versions():
    """检查当前版本"""
    print("🔍 检查当前版本...")
    
    try:
        import numpy
        print(f"NumPy版本: {numpy.__version__}")
        numpy_version = numpy.__version__
    except ImportError:
        print("❌ NumPy未安装")
        numpy_version = None
    
    try:
        import matplotlib
        print(f"matplotlib版本: {matplotlib.__version__}")
        matplotlib_version = matplotlib.__version__
    except ImportError:
        print("❌ matplotlib未安装")
        matplotlib_version = None
    
    return numpy_version, matplotlib_version

def fix_numpy_issue():
    """修复NumPy版本问题"""
    print("🔧 修复NumPy版本冲突...")
    
    # 方法1：降级NumPy
    print("\n方法1：降级NumPy到1.x版本")
    success, stdout, stderr = run_command('pip install "numpy<2"')
    
    if success:
        print("✅ NumPy降级成功")
        return True
    else:
        print(f"❌ NumPy降级失败: {stderr}")
        
        # 方法2：重新安装
        print("\n方法2：重新安装NumPy和matplotlib")
        
        # 卸载
        print("卸载现有包...")
        run_command('pip uninstall numpy matplotlib -y')
        
        # 重新安装
        print("重新安装兼容版本...")
        success, stdout, stderr = run_command('pip install "numpy<2" matplotlib')
        
        if success:
            print("✅ 重新安装成功")
            return True
        else:
            print(f"❌ 重新安装失败: {stderr}")
            return False

def test_imports():
    """测试导入"""
    print("\n🧪 测试导入...")
    
    try:
        import numpy as np
        print(f"✅ NumPy {np.__version__} 导入成功")
    except Exception as e:
        print(f"❌ NumPy导入失败: {e}")
        return False
    
    try:
        import matplotlib
        matplotlib.use('Qt5Agg')
        import matplotlib.pyplot as plt
        print(f"✅ matplotlib {matplotlib.__version__} 导入成功")
    except Exception as e:
        print(f"❌ matplotlib导入失败: {e}")
        return False
    
    # 测试兼容性
    try:
        fig, ax = plt.subplots()
        x = np.linspace(0, 10, 100)
        y = np.sin(x)
        ax.plot(x, y)
        plt.close(fig)
        print("✅ NumPy + matplotlib兼容性测试通过")
        return True
    except Exception as e:
        print(f"❌ 兼容性测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 NumPy版本冲突修复工具")
    print("=" * 50)
    
    # 检查当前版本
    numpy_version, matplotlib_version = check_versions()
    
    if numpy_version and numpy_version.startswith('2.'):
        print(f"\n⚠️ 检测到NumPy 2.x版本 ({numpy_version})")
        print("这可能导致与matplotlib的兼容性问题")
        
        # 询问是否修复
        try:
            choice = input("\n是否修复此问题？(y/n): ").strip().lower()
        except KeyboardInterrupt:
            print("\n👋 用户取消")
            return False
        
        if choice in ['y', 'yes', '是']:
            success = fix_numpy_issue()
            if success:
                print("\n🎉 修复完成！")
                
                # 重新检查版本
                print("\n📋 修复后的版本:")
                check_versions()
                
                # 测试导入
                if test_imports():
                    print("\n✅ 所有测试通过，现在可以运行 python main.py")
                    return True
                else:
                    print("\n⚠️ 仍有问题，可能需要手动处理")
                    return False
            else:
                print("\n❌ 修复失败")
                return False
        else:
            print("\n👋 用户选择不修复")
            return False
    else:
        print(f"\n✅ NumPy版本正常 ({numpy_version})")
        
        # 仍然测试导入
        if test_imports():
            print("\n✅ 所有测试通过")
            return True
        else:
            print("\n⚠️ 存在其他问题")
            return False

if __name__ == '__main__':
    try:
        success = main()
        
        if success:
            print("\n💡 现在可以运行:")
            print("   python main.py")
        else:
            print("\n💡 手动修复建议:")
            print("   pip uninstall numpy matplotlib")
            print('   pip install "numpy<2" matplotlib')
        
        input("\n按回车键退出...")
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\n👋 程序被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        sys.exit(1)
