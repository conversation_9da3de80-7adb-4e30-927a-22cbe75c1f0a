#!/usr/bin/env python3
"""
启动ReaxFFOpt并自动启用监控功能
"""

import os
import sys
import time
import subprocess
import threading
import webbrowser

def start_dashboard():
    """启动监控仪表板"""
    try:
        print("🚀 启动监控仪表板...")
        dashboard_process = subprocess.Popen([
            sys.executable, 'simple_dashboard.py'
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # 等待启动
        time.sleep(3)
        
        # 检查是否启动成功
        try:
            import requests
            response = requests.get('http://localhost:5000/api/data', timeout=2)
            print("✅ 监控仪表板启动成功")
            return True
        except:
            print("⚠️ 监控仪表板启动失败，将使用控制台监控")
            return False
    except Exception as e:
        print(f"⚠️ 启动监控仪表板失败: {e}")
        return False

def open_browser_delayed():
    """延迟打开浏览器"""
    time.sleep(5)
    try:
        webbrowser.open('http://localhost:5000')
        print("🌐 已在浏览器中打开监控仪表板")
    except Exception as e:
        print(f"⚠️ 无法自动打开浏览器: {e}")
        print("📱 请手动访问: http://localhost:5000")

def main():
    """主函数"""
    print("🚀 ReaxFFOpt 监控启动器")
    print("=" * 50)
    
    # 检查文件
    if not os.path.exists('main.py'):
        print("❌ 找不到 main.py 文件")
        print("请确保在 ReaxFFOpt 根目录中运行此脚本")
        return False
    
    if not os.path.exists('monitoring_utils.py'):
        print("❌ 找不到 monitoring_utils.py 文件")
        print("监控功能将不可用")
    else:
        print("✅ 监控工具已就绪")
    
    # 询问用户是否启动Web监控
    print("\n选择监控方式:")
    print("1. 控制台监控（简单，推荐）")
    print("2. Web仪表板监控（图形化）")
    
    try:
        choice = input("请选择 (1/2，默认1): ").strip()
        if not choice:
            choice = "1"
    except KeyboardInterrupt:
        print("\n👋 用户取消")
        return False
    
    dashboard_started = False
    
    if choice == "2":
        # 启动Web监控
        dashboard_started = start_dashboard()
        if dashboard_started:
            # 在后台线程中延迟打开浏览器
            browser_thread = threading.Thread(target=open_browser_delayed)
            browser_thread.daemon = True
            browser_thread.start()
    
    # 启动主程序
    print("\n🚀 启动 ReaxFFOpt 主程序...")
    print("=" * 50)
    
    if dashboard_started:
        print("💡 监控提示:")
        print("   - Web监控: http://localhost:5000")
        print("   - 控制台也会显示监控信息")
    else:
        print("💡 监控提示:")
        print("   - 控制台监控已启用")
        print("   - 优化过程中会显示进度信息")
    
    print("\n🎯 使用说明:")
    print("   1. 导入数据集（文件 → 导入数据集）")
    print("   2. 勾选数据集复选框（自动选择所有参数）")
    print("   3. 或手动勾选想要优化的参数")
    print("   4. 点击'开始优化'按钮")
    print("   5. 监控信息将自动显示")
    
    print("\n" + "=" * 50)
    
    try:
        # 启动主程序
        subprocess.run([sys.executable, 'main.py'])
        return True
    except KeyboardInterrupt:
        print("\n👋 程序被用户中断")
        return True
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        return False

if __name__ == '__main__':
    success = main()
    
    if not success:
        print("\n💡 故障排除:")
        print("1. 确保在 ReaxFFOpt 根目录中运行")
        print("2. 确保已安装所需依赖: pip install PyQt5 numpy flask")
        print("3. 检查 main.py 文件是否存在")
    
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
