#!/usr/bin/env python3
"""
测试监控仪表板
"""

import os
import sys
import time
import webbrowser
import threading

def check_files():
    """检查必要文件"""
    required_files = [
        'dashboard_server.py',
        'templates/dashboard.html'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ 缺少以下文件:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    
    return True

def check_dependencies():
    """检查Python依赖"""
    try:
        import flask
        print(f"✅ Flask: {flask.__version__}")
    except ImportError:
        print("❌ 缺少 Flask")
        return False
    
    try:
        import flask_socketio
        print(f"✅ Flask-SocketIO: {flask_socketio.__version__}")
    except ImportError:
        print("❌ 缺少 Flask-SocketIO")
        return False
    
    try:
        import plotly
        print(f"✅ Plotly: {plotly.__version__}")
    except ImportError:
        print("❌ 缺少 Plotly")
        return False
    
    try:
        import numpy
        print(f"✅ NumPy: {numpy.__version__}")
    except ImportError:
        print("❌ 缺少 NumPy")
        return False
    
    return True

def open_browser_delayed():
    """延迟打开浏览器"""
    time.sleep(3)
    try:
        webbrowser.open('http://localhost:5000')
        print("🌐 已在浏览器中打开监控仪表板")
    except Exception as e:
        print(f"⚠️ 无法自动打开浏览器: {e}")

def test_dashboard():
    """测试仪表板"""
    print("🧪 ReaxFF监控仪表板测试")
    print("=" * 50)
    
    # 检查文件
    print("📁 检查文件...")
    if not check_files():
        return False
    
    # 检查依赖
    print("\n📦 检查依赖...")
    if not check_dependencies():
        print("\n💡 安装缺少的依赖:")
        print("pip install flask flask-socketio plotly numpy")
        return False
    
    print("\n🚀 启动测试服务器...")
    
    # 在后台线程中延迟打开浏览器
    browser_thread = threading.Thread(target=open_browser_delayed)
    browser_thread.daemon = True
    browser_thread.start()
    
    try:
        # 导入并启动服务器
        from dashboard_server import app, socketio
        print("✅ 服务器模块导入成功")
        print("📊 访问地址: http://localhost:5000")
        print("🔧 演示模式: http://localhost:5000/api/start_demo")
        print("⏹️  按 Ctrl+C 停止服务器")
        print("=" * 50)
        
        # 启动服务器
        socketio.run(app, host='0.0.0.0', port=5000, debug=False)
        
    except KeyboardInterrupt:
        print("\n👋 测试服务器已停止")
        return True
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_simple_dashboard():
    """创建简化版仪表板（如果依赖不完整）"""
    print("🔧 创建简化版监控仪表板...")
    
    simple_html = """<!DOCTYPE html>
<html>
<head>
    <title>ReaxFF优化监控 (简化版)</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #1a1a1a; color: white; }
        .container { max-width: 1200px; margin: 0 auto; }
        .metrics { display: grid; grid-template-columns: repeat(4, 1fr); gap: 20px; margin: 20px 0; }
        .metric-card { background: #2a2a2a; padding: 20px; border-radius: 8px; text-align: center; }
        .metric-value { font-size: 2em; font-weight: bold; color: #4CAF50; }
        .status { background: #2a2a2a; padding: 20px; border-radius: 8px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>ReaxFF优化监控仪表板 (简化版)</h1>
        
        <div class="metrics">
            <div class="metric-card">
                <h3>当前迭代</h3>
                <div class="metric-value" id="iteration">0</div>
            </div>
            <div class="metric-card">
                <h3>当前损失</h3>
                <div class="metric-value" id="loss">-</div>
            </div>
            <div class="metric-card">
                <h3>最佳损失</h3>
                <div class="metric-value" id="best-loss">-</div>
            </div>
            <div class="metric-card">
                <h3>状态</h3>
                <div class="metric-value" id="status">待机</div>
            </div>
        </div>
        
        <div class="status">
            <h3>📊 监控状态</h3>
            <p>✅ 简化版监控仪表板已启动</p>
            <p>⚠️ 完整功能需要安装: flask flask-socketio plotly</p>
            <p>💡 运行 <code>pip install flask flask-socketio plotly</code> 安装完整依赖</p>
        </div>
    </div>
    
    <script>
        // 简单的状态更新
        let iteration = 0;
        setInterval(() => {
            iteration++;
            document.getElementById('iteration').textContent = iteration;
            document.getElementById('status').textContent = '运行中';
        }, 1000);
    </script>
</body>
</html>"""
    
    with open('simple_dashboard.html', 'w', encoding='utf-8') as f:
        f.write(simple_html)
    
    print("✅ 简化版仪表板已创建: simple_dashboard.html")
    print("🌐 请在浏览器中打开此文件查看")

def main():
    """主函数"""
    try:
        success = test_dashboard()
        if not success:
            print("\n🔧 创建简化版仪表板作为备选...")
            create_simple_dashboard()
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        print("\n🔧 创建简化版仪表板作为备选...")
        create_simple_dashboard()

if __name__ == '__main__':
    main()
