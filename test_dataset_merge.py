#!/usr/bin/env python3
"""
测试数据集合并功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data.data_handler import DatasetHandler

def test_dataset_merge():
    """测试数据集合并功能"""
    print("🧪 测试数据集合并功能")
    
    # 创建主数据集处理器
    main_handler = DatasetHandler()
    
    # 模拟第一个数据集（cobalt）
    print("\n1. 添加第一个数据集 (cobalt)")
    main_handler.parameters['cobalt'] = {
        'p_2_1_1': {'value': 2.15, 'min': 1.7, 'max': 2.6},
        'p_2_1_4': {'value': 2.15, 'min': 1.7, 'max': 2.6}
    }
    main_handler.dataset_paths['cobalt'] = 'Datasets/cobalt'
    
    print(f"   参数数据集: {list(main_handler.parameters.keys())}")
    print(f"   总参数数量: {sum(len(params) for params in main_handler.parameters.values())}")
    
    # 创建第二个数据集处理器（silica）
    print("\n2. 创建第二个数据集处理器 (silica)")
    new_handler = DatasetHandler()
    new_handler.parameters['silica'] = {
        'p_2_6_6': {'value': 0.53, 'min': 0.3, 'max': 0.76},
        'p_2_6_14': {'value': 3.5, 'min': -1.0, 'max': 8.0}
    }
    new_handler.dataset_paths['silica'] = 'Datasets/silica'
    
    print(f"   新处理器参数数据集: {list(new_handler.parameters.keys())}")
    
    # 模拟合并过程
    print("\n3. 合并数据集")
    
    # 合并参数
    if hasattr(new_handler, 'parameters'):
        for dataset_name, params in new_handler.parameters.items():
            main_handler.parameters[dataset_name] = params
            print(f"   合并参数: {dataset_name} ({len(params)} 个参数)")
    
    # 合并路径
    if hasattr(new_handler, 'dataset_paths'):
        for dataset_name, path in new_handler.dataset_paths.items():
            main_handler.dataset_paths[dataset_name] = path
            print(f"   合并路径: {dataset_name} -> {path}")
    
    # 验证合并结果
    print("\n4. 验证合并结果")
    all_parameters = main_handler.get_all_parameters()
    print(f"   所有参数数据集: {list(all_parameters.keys())}")
    print(f"   总数据集数量: {len(all_parameters)}")
    
    for dataset_name, params in all_parameters.items():
        print(f"   {dataset_name}: {len(params)} 个参数")
        for param_name in list(params.keys())[:2]:  # 只显示前2个参数
            print(f"     - {param_name}: {params[param_name]['value']}")
    
    # 测试结果
    expected_datasets = {'cobalt', 'silica'}
    actual_datasets = set(all_parameters.keys())
    
    if actual_datasets == expected_datasets:
        print("\n✅ 测试通过：数据集合并成功")
        return True
    else:
        print(f"\n❌ 测试失败：期望 {expected_datasets}，实际 {actual_datasets}")
        return False

if __name__ == "__main__":
    test_dataset_merge()
