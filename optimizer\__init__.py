"""
统一优化器模块 - 整合所有优化算法
包括传统优化、强化学习、量子计算等方法
"""

# 导入基础优化器
from .optimizer import Optimizer, PSOOptimizer, create_optimizer
from .multi_objective import MultiObjectiveOptimizer, MultiFidelityOptimizer

# 尝试导入自适应多尺度优化器（需要JAX）
try:
    from .adaptive_multiscale import AdaptiveMultiscaleOptimizer
    ADAPTIVE_AVAILABLE = True
    print("✅ 自适应多尺度优化器导入成功")
except ImportError as e:
    print(f"⚠️  自适应多尺度优化器不可用: {e}")
    AdaptiveMultiscaleOptimizer = None
    ADAPTIVE_AVAILABLE = False

# 导入AI增强的优化器
AI_OPTIMIZERS_AVAILABLE = False
ReinforcementLearningOptimizer = None
FMQAOptimizer = None

try:
    from ml.reinforcement_learning_optimizer import ReinforcementLearningOptimizer
    print("✅ 强化学习优化器导入成功")
    RL_AVAILABLE = True
except ImportError as e:
    print(f"⚠️  强化学习优化器不可用: {e}")
    RL_AVAILABLE = False

try:
    from quantum.quantum_annealing import FMQAOptimizer
    print("✅ 量子退火优化器导入成功")
    QUANTUM_AVAILABLE = True
except ImportError as e:
    print(f"⚠️  完整量子退火优化器不可用: {e}")
    try:
        from quantum.quantum_annealing_simple import FMQAOptimizer
        print("✅ 简化版量子退火优化器导入成功")
        QUANTUM_AVAILABLE = True
    except ImportError as e2:
        print(f"⚠️  简化版量子退火优化器也不可用: {e2}")
        QUANTUM_AVAILABLE = False

AI_OPTIMIZERS_AVAILABLE = RL_AVAILABLE or QUANTUM_AVAILABLE

if not AI_OPTIMIZERS_AVAILABLE:
    print("Warning: AI优化器不可用，仅提供基础优化功能")


class UnifiedOptimizerFactory:
    """统一优化器工厂 - 提供所有优化方法的统一接口"""

    @staticmethod
    def create_optimizer(method: str, **kwargs):
        """创建优化器

        Args:
            method: 优化方法名称
                - 'PSO': 粒子群优化
                - 'MultiObjective': 多目标优化
                - 'MultiFidelity': 多保真度优化
                - 'AdaptiveMultiscale': 自适应多尺度优化
                - 'ReinforcementLearning': 强化学习优化 (需要AI模块)
                - 'QuantumAnnealing': 量子退火优化 (需要AI模块)
            **kwargs: 优化器参数

        Returns:
            优化器实例
        """
        if method == 'PSO':
            return PSOOptimizer(**kwargs)
        elif method == 'MultiObjective':
            return MultiObjectiveOptimizer(**kwargs)
        elif method == 'MultiFidelity':
            return MultiFidelityOptimizer(**kwargs)
        elif method == 'AdaptiveMultiscale' and ADAPTIVE_AVAILABLE:
            return AdaptiveMultiscaleOptimizer(**kwargs)
        elif method == 'ReinforcementLearning' and RL_AVAILABLE:
            return ReinforcementLearningOptimizer(**kwargs)
        elif method == 'QuantumAnnealing' and QUANTUM_AVAILABLE:
            return FMQAOptimizer(**kwargs)
        else:
            available_methods = ['PSO', 'MultiObjective', 'MultiFidelity']
            if ADAPTIVE_AVAILABLE:
                available_methods.append('AdaptiveMultiscale')
            if RL_AVAILABLE:
                available_methods.append('ReinforcementLearning')
            if QUANTUM_AVAILABLE:
                available_methods.append('QuantumAnnealing')
            raise ValueError(f"不支持的优化方法: {method}. 可用方法: {available_methods}")

    @staticmethod
    def get_available_methods():
        """获取可用的优化方法列表"""
        methods = ['PSO', 'MultiObjective', 'MultiFidelity']
        if ADAPTIVE_AVAILABLE:
            methods.append('AdaptiveMultiscale')
        if RL_AVAILABLE:
            methods.append('ReinforcementLearning')
        if QUANTUM_AVAILABLE:
            methods.append('QuantumAnnealing')
        return methods


# 向后兼容的函数
def get_optimizer_factory():
    """获取统一优化器工厂"""
    return UnifiedOptimizerFactory