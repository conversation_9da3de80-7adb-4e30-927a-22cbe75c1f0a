#!/usr/bin/env python3
"""
检查依赖包的兼容性
"""

import sys

def check_numpy():
    """检查NumPy"""
    try:
        import numpy as np
        print(f"✅ NumPy: {np.__version__}")
        return True
    except ImportError as e:
        print(f"❌ NumPy导入失败: {e}")
        return False

def check_matplotlib():
    """检查matplotlib"""
    try:
        import matplotlib
        print(f"✅ matplotlib: {matplotlib.__version__}")
        
        # 测试后端设置
        matplotlib.use('Qt5Agg')
        print("✅ Qt5Agg后端设置成功")
        
        # 测试基本导入
        import matplotlib.pyplot as plt
        from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg
        from matplotlib.figure import Figure
        print("✅ matplotlib组件导入成功")
        
        return True
    except ImportError as e:
        print(f"❌ matplotlib导入失败: {e}")
        return False
    except Exception as e:
        print(f"⚠️ matplotlib配置问题: {e}")
        return False

def check_pyqt5():
    """检查PyQt5"""
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from PyQt5.QtGui import QFont
        print("✅ PyQt5导入成功")
        return True
    except ImportError as e:
        print(f"❌ PyQt5导入失败: {e}")
        return False

def check_compatibility():
    """检查兼容性"""
    try:
        import numpy as np
        import matplotlib
        import matplotlib.pyplot as plt
        
        # 创建简单图形测试
        fig, ax = plt.subplots(figsize=(4, 3))
        x = np.linspace(0, 10, 100)
        y = np.sin(x)
        ax.plot(x, y)
        ax.set_title("兼容性测试")
        
        print("✅ NumPy + matplotlib兼容性测试通过")
        plt.close(fig)
        return True
    except Exception as e:
        print(f"❌ 兼容性测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔍 检查依赖包兼容性")
    print("=" * 40)
    
    print(f"Python版本: {sys.version}")
    print()
    
    # 检查各个包
    numpy_ok = check_numpy()
    matplotlib_ok = check_matplotlib()
    pyqt5_ok = check_pyqt5()
    
    print()
    print("兼容性测试:")
    if numpy_ok and matplotlib_ok:
        compatibility_ok = check_compatibility()
    else:
        compatibility_ok = False
        print("❌ 跳过兼容性测试（缺少依赖）")
    
    print()
    print("=" * 40)
    print("📋 总结:")
    
    if numpy_ok and matplotlib_ok and pyqt5_ok and compatibility_ok:
        print("✅ 所有依赖都正常，可以启用完整功能")
        return True
    else:
        print("⚠️ 存在依赖问题，建议修复:")
        if not numpy_ok:
            print("   - 安装NumPy: pip install numpy")
        if not matplotlib_ok:
            print("   - 安装matplotlib: pip install matplotlib")
        if not pyqt5_ok:
            print("   - 安装PyQt5: pip install PyQt5")
        if not compatibility_ok:
            print("   - 检查版本兼容性")
        return False

if __name__ == '__main__':
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
