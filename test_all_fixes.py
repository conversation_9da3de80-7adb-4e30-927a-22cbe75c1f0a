#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试所有修复功能
验证：
1. 参数行颜色显示
2. 数值显示修复
3. 敏感性分析初始状态
4. 复选框点击改进
5. 学术功能恢复
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_parameter_colors():
    """测试参数行颜色显示"""
    print("🎨 测试参数行颜色显示...")
    
    try:
        from gui.parameter_panel import ParameterTable
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from PyQt5.QtGui import QColor
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建参数表格
        table = ParameterTable()
        
        # 模拟敏感性数据
        sensitivities = {
            'p_1_1_1': 0.8,  # 高敏感性
            'p_1_1_2': 0.5,  # 中敏感性
            'p_1_1_3': 0.2   # 低敏感性
        }
        
        # 添加一些测试行
        table.setRowCount(3)
        for i, (param_name, sensitivity) in enumerate(sensitivities.items()):
            # 添加参数名
            from PyQt5.QtWidgets import QTableWidgetItem
            name_item = QTableWidgetItem(f"└─ {param_name}")
            name_item.setData(Qt.UserRole, {'type': 'parameter', 'name': param_name})
            table.setItem(i, 1, name_item)
            
            # 添加当前值
            value_item = QTableWidgetItem(f"{1.234:.4f}")
            table.setItem(i, 2, value_item)
        
        # 更新敏感性颜色
        table.update_sensitivities(sensitivities)
        
        # 检查颜色是否正确设置
        for i, (param_name, sensitivity) in enumerate(sensitivities.items()):
            sensitivity_item = table.item(i, 5)  # 敏感性列
            if sensitivity_item:
                bg_color = sensitivity_item.background()
                if sensitivity > 0.7:
                    expected_color = QColor(255, 182, 193)  # 高敏感性
                elif sensitivity > 0.4:
                    expected_color = QColor(255, 255, 224)  # 中敏感性
                else:
                    expected_color = QColor(240, 255, 240)  # 低敏感性
                
                print(f"   ✅ {param_name}: 敏感性={sensitivity:.1f}, 颜色已设置")
            else:
                print(f"   ❌ {param_name}: 敏感性列为空")
                return False
        
        print("✅ 参数行颜色显示测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 参数行颜色显示测试失败: {e}")
        return False

def test_value_display():
    """测试数值显示修复"""
    print("🔢 测试数值显示修复...")
    
    try:
        # 模拟参数信息
        param_info_cases = [
            {'value': 1.234, 'min': 0.5, 'max': 2.0},  # 正常情况
            {'default': 0.567, 'min': 0.1, 'max': 1.0},  # 使用default
            1.789,  # 直接数值
            {},  # 空字典，应该生成随机值
        ]
        
        for i, param_info in enumerate(param_info_cases):
            print(f"   测试案例 {i+1}: {param_info}")
            
            # 模拟主窗口的参数值处理逻辑
            if isinstance(param_info, dict) and 'value' in param_info:
                current_value = param_info['value']
            elif isinstance(param_info, (int, float)):
                current_value = float(param_info)
            else:
                # 使用更合理的默认值
                import random
                random.seed(42)  # 固定种子确保一致性
                current_value = random.uniform(0.1, 2.0)
            
            print(f"      → 显示值: {current_value:.4f}")
            
            # 验证值不为0（除非原始值就是0）
            if current_value == 0.0 and param_info != 0:
                print(f"   ❌ 案例 {i+1}: 意外的0值")
                return False
        
        print("✅ 数值显示修复测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 数值显示修复测试失败: {e}")
        return False

def test_sensitivity_initialization():
    """测试敏感性分析初始状态"""
    print("📊 测试敏感性分析初始状态...")
    
    try:
        from gui.enhanced_visualization_panel import EnhancedVisualizationPanel
        from PyQt5.QtWidgets import QApplication
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建可视化面板
        panel = EnhancedVisualizationPanel()
        
        # 检查敏感性数据初始状态
        if hasattr(panel, 'sensitivity_data'):
            if (panel.sensitivity_data['parameters'] == [] and 
                panel.sensitivity_data['sensitivities'] == []):
                print("   ✅ 敏感性数据正确初始化为空")
            else:
                print(f"   ❌ 敏感性数据初始化错误: {panel.sensitivity_data}")
                return False
        else:
            print("   ❌ 敏感性数据属性不存在")
            return False
        
        # 检查是否调用了初始化方法
        if hasattr(panel, 'init_empty_sensitivity_plot'):
            print("   ✅ 敏感性图表初始化方法存在")
        else:
            print("   ❌ 敏感性图表初始化方法不存在")
            return False
        
        print("✅ 敏感性分析初始状态测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 敏感性分析初始状态测试失败: {e}")
        return False

def test_checkbox_improvements():
    """测试复选框改进"""
    print("☑️ 测试复选框改进...")
    
    try:
        from PyQt5.QtWidgets import QTableWidgetItem, QApplication
        from PyQt5.QtCore import Qt
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建改进的复选框项
        checkbox_item = QTableWidgetItem()
        checkbox_item.setFlags(Qt.ItemIsUserCheckable | Qt.ItemIsEnabled | Qt.ItemIsSelectable)
        checkbox_item.setCheckState(Qt.Unchecked)
        checkbox_item.setText("☐")  # Unicode复选框符号
        checkbox_item.setTextAlignment(Qt.AlignCenter)
        
        # 检查属性设置
        if checkbox_item.flags() & Qt.ItemIsSelectable:
            print("   ✅ 复选框可选择属性已设置")
        else:
            print("   ❌ 复选框可选择属性未设置")
            return False
        
        if checkbox_item.text() == "☐":
            print("   ✅ 复选框Unicode符号已设置")
        else:
            print("   ❌ 复选框Unicode符号未设置")
            return False
        
        print("✅ 复选框改进测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 复选框改进测试失败: {e}")
        return False

def test_academic_functions():
    """测试学术功能恢复"""
    print("🎓 测试学术功能恢复...")
    
    try:
        # 检查学术可视化模块
        try:
            from gui.enhanced_academic_visualization import AcademicVisualizationGenerator
            print("   ✅ 学术可视化生成器模块可用")
        except ImportError as e:
            print(f"   ⚠️ 学术可视化生成器模块导入失败: {e}")
        
        # 检查增强可视化集成面板
        try:
            from gui.enhanced_visualization_integration import EnhancedVisualizationIntegrationPanel
            print("   ✅ 增强可视化集成面板模块可用")
        except ImportError as e:
            print(f"   ⚠️ 增强可视化集成面板模块导入失败: {e}")
        
        # 检查主窗口是否有学术功能方法
        from gui.main_window import MainWindow
        
        if hasattr(MainWindow, 'generate_academic_visualizations'):
            print("   ✅ 主窗口学术图表生成方法存在")
        else:
            print("   ❌ 主窗口学术图表生成方法不存在")
            return False
        
        if hasattr(MainWindow, 'generate_academic_report'):
            print("   ✅ 主窗口学术报告生成方法存在")
        else:
            print("   ❌ 主窗口学术报告生成方法不存在")
            return False
        
        print("✅ 学术功能恢复测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 学术功能恢复测试失败: {e}")
        return False

def main():
    """运行所有测试"""
    print("🚀 开始测试所有修复功能...")
    print("=" * 60)
    
    tests = [
        ("参数行颜色显示", test_parameter_colors),
        ("数值显示修复", test_value_display),
        ("敏感性分析初始状态", test_sensitivity_initialization),
        ("复选框改进", test_checkbox_improvements),
        ("学术功能恢复", test_academic_functions),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 40)
        result = test_func()
        results.append((test_name, result))
    
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{len(tests)} 个测试通过")
    
    if passed == len(tests):
        print("\n🎉 所有修复功能测试通过！")
        print("\n📋 修复总结:")
        print("✅ 参数行现在显示正确的颜色（根据数据集和敏感性）")
        print("✅ 参数值显示真实数值而不是0")
        print("✅ 敏感性分析初始状态为空，不显示虚假数据")
        print("✅ 复选框更容易点击，增加了选择区域")
        print("✅ 学术功能已恢复，可通过工具菜单访问")
        
        print("\n🔧 使用说明:")
        print("• 参数颜色：高敏感性=粉红色，中敏感性=黄色，低敏感性=绿色")
        print("• 数据集颜色：cobalt=蓝色，silica=橙色，tnt=紫红色，rdx=绿色")
        print("• 敏感性分析：需要先选择数据集并运行分析")
        print("• 学术功能：工具菜单 → 生成学术图表/学术分析报告")
    else:
        print(f"\n⚠️ {len(tests) - passed} 个测试失败，需要进一步调试")

if __name__ == "__main__":
    main()
