#!/usr/bin/env python3
"""
ReaxFF监控仪表板启动脚本
"""

import os
import sys
import subprocess
import webbrowser
import time
import threading

def check_dependencies():
    """检查依赖包"""
    required_packages = [
        'flask',
        'flask-socketio',
        'plotly',
        'numpy'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ 缺少以下依赖包:")
        for pkg in missing_packages:
            print(f"   - {pkg}")
        print("\n📦 请安装缺少的包:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def open_browser_delayed():
    """延迟打开浏览器"""
    time.sleep(3)  # 等待服务器启动
    try:
        webbrowser.open('http://localhost:5000')
        print("🌐 已在浏览器中打开监控仪表板")
    except Exception as e:
        print(f"⚠️ 无法自动打开浏览器: {e}")
        print("📱 请手动访问: http://localhost:5000")

def main():
    """主函数"""
    print("🚀 ReaxFF优化监控仪表板启动器")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        return False
    
    # 检查dashboard_server.py是否存在
    if not os.path.exists('dashboard_server.py'):
        print("❌ 找不到 dashboard_server.py 文件")
        print("请确保在正确的目录中运行此脚本")
        return False
    
    # 检查templates目录
    if not os.path.exists('templates/dashboard.html'):
        print("❌ 找不到 templates/dashboard.html 文件")
        return False
    
    print("✅ 依赖检查通过")
    print("🔧 启动监控服务器...")
    
    # 在后台线程中延迟打开浏览器
    browser_thread = threading.Thread(target=open_browser_delayed)
    browser_thread.daemon = True
    browser_thread.start()
    
    try:
        # 启动服务器
        subprocess.run([sys.executable, 'dashboard_server.py'])
    except KeyboardInterrupt:
        print("\n👋 监控仪表板已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False
    
    return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
