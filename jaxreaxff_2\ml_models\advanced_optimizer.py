import jax
import jax.numpy as jnp
import numpy as np
import time
import optax
from typing import Dict, List, Tuple, Callable, Any, Optional, Union
from sklearn.gaussian_process import GaussianProcessRegressor
from sklearn.gaussian_process.kernels import Matern, RBF, ConstantKernel
import scipy.optimize as sopt

class SecondOrderOptimizer:
    """
    二阶优化器，使用Hessian矩阵加速收敛
    """
    
    def __init__(
        self,
        learning_rate: float = 0.01,
        damping: float = 1e-4,
        trust_radius: float = 1.0,
        max_iter: int = 100,
        tol: float = 1e-5,
        method: str = 'newton-cg'  # 'newton-cg', 'newton-exact', 'trust-ncg', 'trust-krylov'
    ):
        """
        初始化二阶优化器
        
        Args:
            learning_rate: 学习率
            damping: 阻尼因子，用于稳定Hessian矩阵求逆
            trust_radius: 信任区半径
            max_iter: 最大迭代次数
            tol: 收敛容差
            method: 优化方法
        """
        self.learning_rate = learning_rate
        self.damping = damping
        self.trust_radius = trust_radius
        self.max_iter = max_iter
        self.tol = tol
        self.method = method
        
        # 优化状态
        self.iter_count = 0
        self.best_params = None
        self.best_loss = float('inf')
        self.loss_history = []
    
    def compute_hessian(self, loss_fn: Callable, params: jnp.ndarray) -> jnp.ndarray:
        """
        计算Hessian矩阵
        
        Args:
            loss_fn: 损失函数
            params: 当前参数
            
        Returns:
            hessian: Hessian矩阵
        """
        return jax.hessian(loss_fn)(params)
    
    def newton_step(
        self, 
        params: jnp.ndarray, 
        grad: jnp.ndarray, 
        hessian: jnp.ndarray
    ) -> jnp.ndarray:
        """
        计算牛顿法的参数更新
        
        Args:
            params: 当前参数
            grad: 梯度
            hessian: Hessian矩阵
            
        Returns:
            step: 参数更新量
        """
        # 添加阻尼以确保正定性
        n = hessian.shape[0]
        damped_hessian = hessian + self.damping * jnp.eye(n)
        
        # 求解线性方程组: H * step = -grad
        step = jnp.linalg.solve(damped_hessian, -grad)
        
        # 应用信任区约束
        step_norm = jnp.linalg.norm(step)
        if step_norm > self.trust_radius:
            step = step * (self.trust_radius / step_norm)
            
        return step
    
    def trust_region_step(
        self, 
        loss_fn: Callable, 
        params: jnp.ndarray, 
        grad: jnp.ndarray, 
        hessian: jnp.ndarray
    ) -> jnp.ndarray:
        """
        使用信任域方法计算参数更新
        
        Args:
            loss_fn: 损失函数
            params: 当前参数
            grad: 梯度
            hessian: Hessian矩阵
            
        Returns:
            step: 参数更新量
        """
        n = params.shape[0]
        
        # 定义信任域子问题
        def subproblem(p):
            p = np.asarray(p)
            q = grad.dot(p) + 0.5 * p.dot(hessian.dot(p))
            return q
            
        def subproblem_grad(p):
            p = np.asarray(p)
            return grad + hessian.dot(p)
            
        def subproblem_hessp(p, v):
            v = np.asarray(v)
            return hessian.dot(v)
        
        # 使用scipy的信任域方法
        result = sopt.minimize(
            subproblem,
            np.zeros(n),
            method='trust-krylov' if self.method == 'trust-krylov' else 'trust-ncg',
            jac=subproblem_grad,
            hessp=subproblem_hessp,
            bounds=sopt.Bounds(-self.trust_radius, self.trust_radius),
            options={'maxiter': 100}
        )
        
        return jnp.array(result.x)
    
    def optimize(
        self, 
        loss_fn: Callable,
        initial_params: jnp.ndarray,
        callback: Optional[Callable] = None
    ) -> Tuple[jnp.ndarray, float, List[float]]:
        """
        优化参数
        
        Args:
            loss_fn: 损失函数
            initial_params: 初始参数
            callback: 回调函数，接收当前参数和损失值
            
        Returns:
            best_params: 最优参数
            best_loss: 最优损失值
            loss_history: 损失历史
        """
        params = initial_params
        self.iter_count = 0
        self.best_params = params
        self.best_loss = loss_fn(params)
        self.loss_history = [self.best_loss]
        
        grad_fn = jax.grad(loss_fn)
        
        for i in range(self.max_iter):
            # 计算当前损失和梯度
            loss = loss_fn(params)
            grad = grad_fn(params)
            
            # 梯度范数
            grad_norm = jnp.linalg.norm(grad)
            
            # 检查收敛条件
            if grad_norm < self.tol:
                break
                
            # 根据方法选择更新策略
            if self.method in ['newton-cg', 'newton-exact']:
                # 计算Hessian矩阵
                hessian = self.compute_hessian(loss_fn, params)
                step = self.newton_step(params, grad, hessian)
            elif self.method in ['trust-ncg', 'trust-krylov']:
                # 计算Hessian矩阵
                hessian = self.compute_hessian(loss_fn, params)
                step = self.trust_region_step(loss_fn, params, grad, hessian)
            else:
                raise ValueError(f"Unsupported method: {self.method}")
            
            # 更新参数
            params = params + step
            
            # 计算新损失
            new_loss = loss_fn(params)
            self.loss_history.append(new_loss)
            
            # 更新最佳参数
            if new_loss < self.best_loss:
                self.best_loss = new_loss
                self.best_params = params
            
            # 调用回调函数
            if callback is not None:
                callback(params, new_loss)
                
            self.iter_count += 1
            
        return self.best_params, self.best_loss, self.loss_history

class BayesianOptimizer:
    """
    贝叶斯优化器，使用高斯过程对目标函数进行建模
    """
    
    def __init__(
        self,
        bounds: List[Tuple[float, float]],
        n_initial_points: int = 10,
        acquisition_function: str = 'ei',  # 'ei', 'ucb', 'pi'
        exploitation_weight: float = 0.1,
        random_state: Optional[int] = None,
        noise_level: float = 1e-6,
        verbose: bool = False
    ):
        """
        初始化贝叶斯优化器
        
        Args:
            bounds: 参数搜索空间的边界，为每个维度指定(min, max)
            n_initial_points: 初始采样点数量
            acquisition_function: 采集函数类型
            exploitation_weight: 探索-利用权衡参数
            random_state: 随机种子
            noise_level: 观测噪声水平
            verbose: 是否显示详细信息
        """
        self.bounds = np.array(bounds)
        self.n_initial_points = n_initial_points
        self.acquisition_function = acquisition_function
        self.exploitation_weight = exploitation_weight
        self.random_state = random_state
        self.noise_level = noise_level
        self.verbose = verbose
        
        # 设置随机状态
        self.rng = np.random.RandomState(random_state)
        
        # 高斯过程回归器
        self.gpr = None
        self._init_model()
        
        # 优化状态
        self.X_samples = []  # 采样点
        self.y_samples = []  # 观测值
        self.best_params = None  # 最佳参数
        self.best_value = float('inf')  # 最佳值
        self.n_dims = len(bounds)  # 参数空间维度
    
    def _init_model(self):
        """初始化高斯过程模型"""
        # 构建核函数
        amplitude = ConstantKernel(constant_value=1.0)
        length_scale = np.ones(self.n_dims)
        kernel = amplitude * Matern(length_scale=length_scale, nu=2.5)
        
        # 创建高斯过程回归器
        self.gpr = GaussianProcessRegressor(
            kernel=kernel,
            alpha=self.noise_level,
            normalize_y=True,
            n_restarts_optimizer=5,
            random_state=self.random_state
        )
    
    def _normalize_params(self, params: np.ndarray) -> np.ndarray:
        """
        将参数归一化到[0, 1]范围
        
        Args:
            params: 原始参数
            
        Returns:
            norm_params: 归一化后的参数
        """
        mins = self.bounds[:, 0]
        maxs = self.bounds[:, 1]
        return (params - mins) / (maxs - mins)
    
    def _denormalize_params(self, norm_params: np.ndarray) -> np.ndarray:
        """
        将归一化的参数转换回原始范围
        
        Args:
            norm_params: 归一化后的参数
            
        Returns:
            params: 原始参数
        """
        mins = self.bounds[:, 0]
        maxs = self.bounds[:, 1]
        return norm_params * (maxs - mins) + mins
    
    def _create_initial_points(self) -> np.ndarray:
        """
        创建初始采样点
        
        Returns:
            initial_points: 初始采样点
        """
        # 使用拉丁超立方采样
        from sklearn.utils.fixes import sp_version
        if sp_version >= (1, 6, 0):
            from scipy.stats import qmc
            sampler = qmc.LatinHypercube(d=self.n_dims, seed=self.random_state)
            samples = sampler.random(n=self.n_initial_points)
        else:
            # 如果scipy版本较低，使用均匀采样
            samples = self.rng.rand(self.n_initial_points, self.n_dims)
            
        # 将[0, 1]范围的样本转换到参数空间
        return self._denormalize_params(samples)
    
    def _expected_improvement(
        self, 
        x: np.ndarray, 
        y_best: float,
        xi: float = 0.01
    ) -> float:
        """
        计算期望改进(Expected Improvement)
        
        Args:
            x: 预测点
            y_best: 当前最佳观测值
            xi: 探索参数
            
        Returns:
            ei: 期望改进值
        """
        x = x.reshape(1, -1)
        mu, sigma = self.gpr.predict(x, return_std=True)
        
        # 标准化提升量
        with np.errstate(divide='warn'):
            imp = y_best - mu - xi
            Z = imp / sigma
            ei = imp * scipy.stats.norm.cdf(Z) + sigma * scipy.stats.norm.pdf(Z)
            ei[sigma == 0.0] = 0.0
            
        return ei[0]
    
    def _upper_confidence_bound(
        self, 
        x: np.ndarray, 
        beta: float = 2.0
    ) -> float:
        """
        计算上置信界(Upper Confidence Bound)
        
        Args:
            x: 预测点
            beta: 探索权重
            
        Returns:
            ucb: UCB值
        """
        x = x.reshape(1, -1)
        mu, sigma = self.gpr.predict(x, return_std=True)
        
        return mu[0] - beta * sigma[0]  # 负号使其适用于最小化问题
    
    def _probability_improvement(
        self, 
        x: np.ndarray, 
        y_best: float,
        xi: float = 0.01
    ) -> float:
        """
        计算改进概率(Probability of Improvement)
        
        Args:
            x: 预测点
            y_best: 当前最佳观测值
            xi: 探索参数
            
        Returns:
            pi: 改进概率
        """
        x = x.reshape(1, -1)
        mu, sigma = self.gpr.predict(x, return_std=True)
        
        with np.errstate(divide='warn'):
            Z = (y_best - mu - xi) / sigma
            pi = scipy.stats.norm.cdf(Z)
            
        return pi[0]
    
    def _acquisition(
        self,
        x: np.ndarray,
        y_best: float
    ) -> float:
        """
        计算采集函数值
        
        Args:
            x: 预测点
            y_best: 当前最佳观测值
            
        Returns:
            acq: 采集函数值
        """
        if self.acquisition_function == 'ei':
            return -self._expected_improvement(x, y_best, xi=self.exploitation_weight)
        elif self.acquisition_function == 'ucb':
            return -self._upper_confidence_bound(x, beta=self.exploitation_weight)
        elif self.acquisition_function == 'pi':
            return -self._probability_improvement(x, y_best, xi=self.exploitation_weight)
        else:
            raise ValueError(f"Unknown acquisition function: {self.acquisition_function}")
    
    def _find_next_point(self) -> np.ndarray:
        """
        找到下一个采样点
        
        Returns:
            next_point: 下一个采样点
        """
        # 当前最佳观测值
        y_best = np.min(self.y_samples)
        
        # 定义采集函数
        def acquisition_fn(x):
            return self._acquisition(x, y_best)
        
        # 从不同起点开始优化
        n_restarts = 5
        best_x = None
        best_acquisition_value = 1.0  # 初始化为较大值以便第一次比较
        bounds = [(0.0, 1.0) for _ in range(self.n_dims)]
        
        # 尝试从不同的起点开始
        for _ in range(n_restarts):
            # 随机初始点
            x0 = self.rng.rand(self.n_dims)
            
            # 优化采集函数
            result = sopt.minimize(
                acquisition_fn,
                x0=x0,
                bounds=bounds,
                method='L-BFGS-B'
            )
            
            if result.fun < best_acquisition_value:
                best_acquisition_value = result.fun
                best_x = result.x
                
        # 将归一化参数转换回原始范围
        return self._denormalize_params(best_x)
    
    def fit_model(self):
        """拟合高斯过程模型"""
        X = np.array(self.X_samples)
        y = np.array(self.y_samples)
        
        # 归一化参数
        X_norm = np.vstack([self._normalize_params(x) for x in X])
        
        # 拟合模型
        self.gpr.fit(X_norm, y)
    
    def run_initial_points(
        self, 
        objective_function: Callable[[np.ndarray], float]
    ):
        """
        评估初始点
        
        Args:
            objective_function: 目标函数
        """
        # 创建初始点
        initial_points = self._create_initial_points()
        
        # 评估初始点
        for i, params in enumerate(initial_points):
            value = objective_function(params)
            
            self.X_samples.append(params)
            self.y_samples.append(value)
            
            if value < self.best_value:
                self.best_value = value
                self.best_params = params
                
            if self.verbose:
                print(f"Initial point {i+1}/{len(initial_points)}: value = {value:.6f}")
    
    def optimize(
        self, 
        objective_function: Callable[[np.ndarray], float],
        n_iterations: int,
        callback: Optional[Callable] = None
    ) -> Tuple[np.ndarray, float, List[Tuple[np.ndarray, float]]]:
        """
        运行贝叶斯优化
        
        Args:
            objective_function: 目标函数
            n_iterations: 迭代次数
            callback: 回调函数，接收当前参数和值
            
        Returns:
            best_params: 最优参数
            best_value: 最优值
            history: 优化历史
        """
        # 重置优化状态
        self.X_samples = []
        self.y_samples = []
        self.best_params = None
        self.best_value = float('inf')
        
        # 评估初始点
        self.run_initial_points(objective_function)
        
        # 拟合初始模型
        self.fit_model()
        
        start_time = time.time()
        
        # 主循环
        for i in range(n_iterations):
            # 找到下一个采样点
            next_point = self._find_next_point()
            
            # 评估新点
            value = objective_function(next_point)
            
            # 更新历史
            self.X_samples.append(next_point)
            self.y_samples.append(value)
            
            # 更新最佳结果
            if value < self.best_value:
                self.best_value = value
                self.best_params = next_point
                
            # 重新拟合模型
            self.fit_model()
            
            # 调用回调函数
            if callback:
                callback(next_point, value)
                
            # 打印进度
            if self.verbose:
                elapsed = time.time() - start_time
                print(f"Iteration {i+1}/{n_iterations}: value = {value:.6f}, "
                      f"best = {self.best_value:.6f}, time = {elapsed:.2f}s")
        
        # 构建优化历史
        history = list(zip(self.X_samples, self.y_samples))
        
        return self.best_params, self.best_value, history

class MultiTaskOptimizer:
    """
    多任务优化器，用于同时优化多个相关任务
    """
    
    def __init__(
        self,
        task_weights: Dict[str, float],
        bounds: List[Tuple[float, float]],
        optimizer_type: str = 'bayesian',  # 'bayesian' or 'second_order'
        n_initial_points: int = 10,
        learning_rate: float = 0.01,
        max_iter: int = 100,
        verbose: bool = False,
        random_state: Optional[int] = None
    ):
        """
        初始化多任务优化器
        
        Args:
            task_weights: 任务权重字典，键为任务名称，值为权重
            bounds: 参数搜索空间的边界
            optimizer_type: 优化器类型
            n_initial_points: 初始采样点数量 (贝叶斯优化)
            learning_rate: 学习率 (二阶优化)
            max_iter: 最大迭代次数
            verbose: 是否显示详细信息
            random_state: 随机种子
        """
        self.task_weights = task_weights
        self.bounds = bounds
        self.optimizer_type = optimizer_type
        self.n_initial_points = n_initial_points
        self.learning_rate = learning_rate
        self.max_iter = max_iter
        self.verbose = verbose
        self.random_state = random_state
        
        # 创建优化器
        if optimizer_type == 'bayesian':
            self.optimizer = BayesianOptimizer(
                bounds=bounds,
                n_initial_points=n_initial_points,
                verbose=verbose,
                random_state=random_state
            )
        elif optimizer_type == 'second_order':
            self.optimizer = SecondOrderOptimizer(
                learning_rate=learning_rate,
                max_iter=max_iter
            )
        else:
            raise ValueError(f"Unknown optimizer type: {optimizer_type}")
            
        # 优化状态
        self.best_params = None
        self.best_values = {}
        self.history = []
    
    def _weighted_objective(
        self,
        params: Union[np.ndarray, jnp.ndarray],
        tasks: Dict[str, Callable]
    ) -> Union[float, jnp.ndarray]:
        """
        计算加权目标函数
        
        Args:
            params: 参数
            tasks: 任务字典，键为任务名称，值为任务目标函数
            
        Returns:
            weighted_loss: 加权损失
        """
        weighted_loss = 0.0
        task_values = {}
        
        for task_name, task_fn in tasks.items():
            # 计算任务损失
            task_value = task_fn(params)
            task_values[task_name] = task_value
            
            # 加权求和
            weight = self.task_weights.get(task_name, 1.0)
            weighted_loss += weight * task_value
            
        # 记录每个任务的值
        self.current_task_values = task_values
        
        return weighted_loss
    
    def optimize(
        self,
        tasks: Dict[str, Callable],
        callback: Optional[Callable] = None
    ) -> Tuple[np.ndarray, Dict[str, float], List]:
        """
        运行多任务优化
        
        Args:
            tasks: 任务字典，键为任务名称，值为任务目标函数
            callback: 回调函数
            
        Returns:
            best_params: 最优参数
            best_values: 每个任务的最优值
            history: 优化历史
        """
        # 定义加权目标函数
        def objective(params):
            return self._weighted_objective(params, tasks)
            
        # 自定义回调
        self.current_task_values = {}
        
        def internal_callback(params, value):
            # 记录所有任务的值
            task_values = self.current_task_values.copy()
            
            # 构建历史记录
            history_entry = {
                'params': params,
                'weighted_value': value,
                'task_values': task_values
            }
            
            self.history.append(history_entry)
            
            # 调用用户回调
            if callback:
                callback(params, value, task_values)
        
        # 运行优化
        if self.optimizer_type == 'bayesian':
            self.best_params, _, _ = self.optimizer.optimize(
                objective_function=objective,
                n_iterations=self.max_iter,
                callback=internal_callback
            )
        else:  # second_order
            # 为二阶优化器创建初始参数
            n_dims = len(self.bounds)
            initial_params = np.zeros(n_dims)
            for i, (lower, upper) in enumerate(self.bounds):
                initial_params[i] = (lower + upper) / 2
                
            self.best_params, _, _ = self.optimizer.optimize(
                loss_fn=objective,
                initial_params=jnp.array(initial_params),
                callback=internal_callback
            )
            
        # 计算每个任务的最优值
        for task_name, task_fn in tasks.items():
            self.best_values[task_name] = task_fn(self.best_params)
            
        return self.best_params, self.best_values, self.history 