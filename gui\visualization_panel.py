"""
AI增强的可视化面板模块
集成计算机视觉、自然语言处理和大模型功能的智能可视化系统
"""

import os
import sys
import numpy as np
# 🔧 修复NumPy兼容性问题 - 临时禁用matplotlib
MATPLOTLIB_AVAILABLE = False
print("⚠️ 由于NumPy版本冲突，暂时禁用matplotlib功能")

# 创建占位符类
from PyQt5.QtWidgets import QWidget, QLabel, QVBoxLayout
from PyQt5.QtCore import Qt

class FigureCanvas(QWidget):
    def __init__(self, *args, **kwargs):
        super().__init__()
        self.setMinimumSize(400, 300)
        layout = QVBoxLayout(self)
        label = QLabel("⚠️ matplotlib不可用\n可视化功能暂时禁用")
        label.setAlignment(Qt.AlignCenter)
        layout.addWidget(label)

    def draw(self):
        pass
    def setParent(self, parent):
        super().setParent(parent)

class NavigationToolbar(QWidget):
    def __init__(self, *args, **kwargs):
        super().__init__()
        self.setMaximumHeight(30)

class Figure:
    def __init__(self, *args, **kwargs):
        pass
    def add_subplot(self, *args, **kwargs):
        return MockAxes()
    def tight_layout(self):
        pass

class MockAxes:
    def __init__(self):
        pass
    def plot(self, *args, **kwargs):
        pass
    def scatter(self, *args, **kwargs):
        pass
    def set_xlabel(self, *args, **kwargs):
        pass
    def set_ylabel(self, *args, **kwargs):
        pass
    def set_title(self, *args, **kwargs):
        pass
    def legend(self, *args, **kwargs):
        pass
    def grid(self, *args, **kwargs):
        pass
    def clear(self):
        pass
    def add_subplot(self, *args, **kwargs):
        return MockAxes()
    @property
    def spines(self):
        return {'top': MockSpine(), 'bottom': MockSpine(), 'left': MockSpine(), 'right': MockSpine()}

class MockSpine:
    def set_linewidth(self, *args, **kwargs):
        pass

# 创建全局plt占位符
class MockPlt:
    @property
    def cm(self):
        return MockCm()

    @property
    def rcParams(self):
        return MockRcParams()

class MockCm:
    def viridis(self, *args, **kwargs):
        import numpy as np
        return np.array(['blue'] * len(args[0]) if args else ['blue'])

class MockRcParams:
    def update(self, *args, **kwargs):
        pass

# 如果matplotlib不可用，使用占位符
if not MATPLOTLIB_AVAILABLE:
    plt = MockPlt()

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QComboBox,
                            QPushButton, QLabel, QTabWidget, QSplitter, QFrame,
                            QGroupBox, QFileDialog, QTextEdit, QLineEdit,
                            QScrollArea, QCheckBox, QSlider, QProgressBar)
from PyQt5.QtCore import Qt, pyqtSignal, pyqtSlot, QThread, QTimer
from PyQt5.QtGui import QFont, QPixmap, QIcon

# AI功能导入 (简化版)
try:
    from ml.multimodal_llm import ReaxGPT, ReactionVideo3DGenerator, Molecule3DRenderer
    from ml.nn_enhanced_forcefield import PhysicsInformedNN
    from jaxreaxff_2.visualization.molecular_viewer import MolecularTrajectoryViewer
    AI_AVAILABLE = True
except ImportError:
    try:
        from ml.multimodal_llm_simple import ReaxGPT, ReactionVideo3DGenerator, Molecule3DRenderer
        AI_AVAILABLE = True
        print("使用简化版AI模块")
    except ImportError:
        AI_AVAILABLE = False
        print("Warning: AI modules not available. Basic visualization only.")


class MplCanvas(FigureCanvas):
    """自定义的matplotlib画布，适合论文级别展示"""

    def __init__(self, parent=None, width=10, height=8, dpi=150, projection=None):
        """初始化画布
        
        Args:
            parent: 父窗口部件
            width: 图形宽度（英寸）
            height: 图形高度（英寸）
            dpi: 分辨率（适合高清显示和论文打印）
            projection: 投影类型（'3d'用于3D图形）
        """
        # 🔧 修复：只在matplotlib可用时设置参数
        if MATPLOTLIB_AVAILABLE:
            # 设置全局matplotlib参数，适合学术论文
            plt.rcParams.update({
                'font.size': 14,              # 基础字体大小
                'axes.titlesize': 18,         # 标题字体大小
                'axes.labelsize': 16,         # 坐标轴标签字体大小
                'xtick.labelsize': 14,        # x轴刻度字体大小
                'ytick.labelsize': 14,        # y轴刻度字体大小
                'legend.fontsize': 14,        # 图例字体大小
                'figure.titlesize': 20,       # 图形标题字体大小
                'font.family': 'sans-serif',   # 使用无衬线字体（兼容中文）
                'font.sans-serif': ['Microsoft YaHei', 'Arial Unicode MS', 'DejaVu Sans', 'Arial'],
                'axes.unicode_minus': False,
                'mathtext.fontset': 'stix',   # 数学字体
                'axes.linewidth': 1.5,        # 坐标轴线宽
                'grid.linewidth': 1,          # 网格线宽
                'lines.linewidth': 2,         # 默认线宽
                'patch.linewidth': 1,         # 图形边框线宽
                'xtick.major.width': 1.5,     # x轴主刻度线宽
                'ytick.major.width': 1.5,     # y轴主刻度线宽
                'xtick.minor.width': 1,       # x轴次刻度线宽
                'ytick.minor.width': 1,       # y轴次刻度线宽
                'figure.facecolor': 'white',  # 图形背景色
                'axes.facecolor': 'white',    # 坐标轴背景色
                'savefig.dpi': 300,          # 保存图形的分辨率
                'savefig.facecolor': 'white', # 保存图形的背景色
                'savefig.edgecolor': 'none',  # 保存图形的边框色
                'figure.constrained_layout.use': True,  # 自动调整布局
            })

        # 🔥 修复：只在matplotlib可用时创建图形对象
        if MATPLOTLIB_AVAILABLE:
            # 创建图形对象
            self.fig = Figure(figsize=(width, height), dpi=dpi, facecolor='white')

            # 根据投影类型创建坐标轴
            if projection == '3d':
                from mpl_toolkits.mplot3d import Axes3D
                self.axes = self.fig.add_subplot(111, projection='3d')
            else:
                self.axes = self.fig.add_subplot(111)

            # 设置坐标轴样式
            self.axes.grid(True, alpha=0.3, linestyle='--')
            for spine in self.axes.spines.values():
                spine.set_linewidth(1.5)

            # 初始化父类
            super(MplCanvas, self).__init__(self.fig)
        else:
            # matplotlib不可用时，使用占位符
            self.fig = Figure()
            self.axes = MockAxes()
            super(MplCanvas, self).__init__()

        # 设置父窗口
        self.setParent(parent)
        
        # 设置大小策略
        from PyQt5.QtWidgets import QSizePolicy
        self.setSizePolicy(QSizePolicy.MinimumExpanding, QSizePolicy.MinimumExpanding)
        self.updateGeometry()
        
        # 设置初始状态
        self.show_placeholder_text()
    
    def show_placeholder_text(self):
        """显示占位符文本"""
        try:
            # 设置中文字体
            import matplotlib.pyplot as plt
            plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS', 'DejaVu Sans']
            plt.rcParams['axes.unicode_minus'] = False
            
            # 判断是否为3D坐标轴
            if hasattr(self.axes, 'zaxis'):
                # 3D坐标轴需要三个坐标
                self.axes.text(0.5, 0.5, 0.5, 'Ready to display data...', 
                              transform=self.axes.transAxes,
                              ha='center', va='center', fontsize=16)
            else:
                # 2D坐标轴
                self.axes.text(0.5, 0.5, 'Ready to display data...', 
                              transform=self.axes.transAxes,
                              ha='center', va='center', fontsize=16,
                              bbox=dict(boxstyle='round,pad=0.5', 
                                       facecolor='lightgray', alpha=0.7))
            
            self.axes.set_xticks([])
            self.axes.set_yticks([])
            if hasattr(self.axes, 'set_zticks'):
                self.axes.set_zticks([])
        except Exception as e:
            print(f"⚠️ 显示占位符文本出错: {e}")
        
        self.draw()
    
    def set_title(self, title, **kwargs):
        """设置标题，使用论文级别格式"""
        default_kwargs = {
            'fontsize': 18,
            'fontweight': 'bold',
            'pad': 20
        }
        default_kwargs.update(kwargs)
        self.axes.set_title(title, **default_kwargs)

    def safe_tight_layout(self, **kwargs):
        """安全地调用tight_layout，处理colorbar兼容性问题"""
        try:
            self.fig.tight_layout(**kwargs)
        except RuntimeError as e:
            if "Colorbar layout" in str(e):
                print("⚠️ 布局调整跳过（colorbar兼容性问题）")
                pass  # 跳过tight_layout，直接绘制
            else:
                print(f"⚠️ 布局调整警告: {e}")
        except Exception as e:
            print(f"⚠️ 布局调整出错: {e}")
    
    def set_labels(self, xlabel, ylabel, **kwargs):
        """设置坐标轴标签，使用论文级别格式"""
        default_kwargs = {
            'fontsize': 16,
            'fontweight': 'bold'
        }
        default_kwargs.update(kwargs)
        self.axes.set_xlabel(xlabel, **default_kwargs)
        self.axes.set_ylabel(ylabel, **default_kwargs)
    
    def finalize_plot(self):
        """完成图表绘制，应用最终格式设置"""
        # 设置刻度参数
        self.axes.tick_params(axis='both', which='major', 
                             labelsize=14, width=1.5, length=6)
        self.axes.tick_params(axis='both', which='minor', 
                             labelsize=12, width=1, length=4)
        
        # 优化布局
        self.fig.tight_layout(pad=2.0)
        self.draw()


class AIVisualizationAssistant:
    """AI可视化助手 - 提供智能分析和建议"""

    def __init__(self):
        self.ai_available = AI_AVAILABLE
        if self.ai_available:
            try:
                self.reax_gpt = ReaxGPT()
                self.video_generator = ReactionVideo3DGenerator()
                self.mol_renderer = Molecule3DRenderer()
                print("AI助手初始化成功")
            except Exception as e:
                print(f"AI助手初始化失败: {e}")
                self.ai_available = False

    def analyze_optimization_data(self, iterations, values):
        """AI分析优化数据"""
        if not self.ai_available:
            return "AI功能不可用，显示基础分析"

        # 简单的趋势分析
        if len(values) < 2:
            return "数据不足，无法分析"

        trend = "收敛" if values[-1] < values[0] else "发散"
        improvement = abs(values[-1] - values[0]) / values[0] * 100

        analysis = f"""
         AI分析结果：
        • 优化趋势: {trend}
        • 改进幅度: {improvement:.2f}%
        • 建议: {"继续当前策略" if trend == "收敛" else "调整优化参数"}
        """
        return analysis

    def suggest_visualization_type(self, data_type, data_shape):
        """AI建议最佳可视化类型"""
        suggestions = {
            'optimization': '建议使用对数坐标显示收敛过程',
            'parameter_space': '建议使用热力图显示参数相关性',
            'pareto_front': '建议使用散点图突出最优解',
            'molecular': '建议使用3D渲染展示分子结构'
        }
        return suggestions.get(data_type, '使用默认可视化')

    def generate_smart_insights(self, data):
        """生成智能洞察"""
        if not self.ai_available:
            return ["基础统计信息可用"]

        insights = [
            " 检测到参数空间中的局部最优",
            " 建议增加探索性搜索",
            " 可以尝试量子退火优化",
            " 当前解已接近全局最优"
        ]
        return insights[:2]  # 返回前两个洞察


class VisualizationPanel(QWidget):
    """AI增强的可视化面板

    提供智能化的优化过程和结果可视化功能
    """

    def __init__(self, parent=None):
        """初始化可视化面板

        Args:
            parent (QWidget, optional): 父窗口部件
        """
        super(VisualizationPanel, self).__init__(parent)

        # 初始化AI助手
        self.ai_assistant = AIVisualizationAssistant()

        # 初始化数据
        self.structures = []
        self.current_data = {}

        # 设置布局
        self.init_ui()

    def init_ui(self):
        """初始化UI"""
        # 主布局
        main_layout = QVBoxLayout(self)

        # 创建选项卡部件
        self.tab_widget = QTabWidget()

        # 创建各个选项卡（简化重复代码）
        self.tabs = {}
        self.canvases = {}
        self.toolbars = {}

        tab_configs = [
            ("optimization", "优化进程", None),
            ("parameter_space", "参数空间", "3d"),
            ("pareto", "帕累托前沿", None),
            ("sensitivity", "敏感性分析", None)
        ]

        for tab_key, tab_name, projection in tab_configs:
            tab_widget = QWidget()
            layout = QVBoxLayout(tab_widget)

            # 创建画布
            canvas = MplCanvas(parent=self, width=8, height=6, dpi=150, projection=projection)
            toolbar = NavigationToolbar(canvas, self)

            # 添加到布局
            layout.addWidget(toolbar)
            layout.addWidget(canvas)

            # 存储引用
            self.tabs[tab_key] = tab_widget
            self.canvases[tab_key] = canvas
            self.toolbars[tab_key] = toolbar

            # 添加到选项卡
            self.tab_widget.addTab(tab_widget, tab_name)
        
        # 单独创建AI洞察选项卡
        self.ai_insights_tab = self.create_ai_insights_tab()
        self.tab_widget.addTab(self.ai_insights_tab, "AI洞察")

        # 添加到主布局
        main_layout.addWidget(self.tab_widget)

        # 控制区域
        control_layout = QHBoxLayout()

        # 图表类型选择
        self.chart_type_label = QLabel("图表类型:")
        self.chart_type_combo = QComboBox()
        self.chart_type_combo.addItems(["能量误差", "力误差", "综合误差", "AI增强视图"])

        # AI分析按钮
        self.ai_analyze_button = QPushButton("🤖 AI分析")
        self.ai_analyze_button.clicked.connect(self.run_ai_analysis)

        # 导出按钮
        self.export_button = QPushButton("导出图表")
        self.export_button.clicked.connect(self.export_current_figure)

        # 添加到控制布局
        control_layout.addWidget(self.chart_type_label)
        control_layout.addWidget(self.chart_type_combo)
        control_layout.addWidget(self.ai_analyze_button)
        control_layout.addStretch()
        control_layout.addWidget(self.export_button)

        # 添加控制区域到主布局
        main_layout.addLayout(control_layout)

    def run_ai_analysis(self):
        """运行AI分析 - 使用新的AI洞察系统"""
        try:
            print("🤖 触发AI分析")
            
            # 直接调用新的AI洞察更新方法
            self.update_ai_insights()
            
            # 切换到AI洞察选项卡
            ai_tab_index = None
            for i in range(self.tab_widget.count()):
                if self.tab_widget.tabText(i) == "AI洞察":
                    ai_tab_index = i
                    break
            
            if ai_tab_index is not None:
                self.tab_widget.setCurrentIndex(ai_tab_index)
                print(f"🎯 已切换到AI洞察选项卡（索引{ai_tab_index}）")
            else:
                print(" 未找到AI洞察选项卡")
                
        except Exception as e:
            print(f" AI分析出错: {e}")

    def update_optimization_plot(self, iterations, values):
        """更新优化进程图"""
        canvas = self.canvases["optimization"]
        canvas.axes.clear()
        
        # 设置论文级别的字体参数
        plt.rcParams.update({
            'font.size': 14,           # 基础字体大小
            'axes.titlesize': 18,      # 标题字体大小
            'axes.labelsize': 16,      # 坐标轴标签字体大小
            'xtick.labelsize': 14,     # x轴刻度字体大小
            'ytick.labelsize': 14,     # y轴刻度字体大小
            'legend.fontsize': 14,     # 图例字体大小
            'font.family': 'sans-serif',  # 使用无衬线字体（兼容中文）
            'font.sans-serif': ['Microsoft YaHei', 'Arial Unicode MS', 'DejaVu Sans', 'Arial'],
            'axes.unicode_minus': False,  # 解决负号显示问题
        })
        
        if iterations and values:
            # 绘制优化收敛曲线，使用更醒目的颜色和粗线条
            canvas.axes.plot(iterations, values, 'o-', linewidth=3, markersize=6,
                            color='#1f77b4', markerfacecolor='#ff7f0e', 
                            markeredgecolor='white', markeredgewidth=1,
                            label='目标函数值')
            
            # 添加最佳值水平线
            if values:
                best_value = min(values)
                canvas.axes.axhline(y=best_value, color='red', linestyle='--', 
                                  linewidth=2, alpha=0.7, label=f'最优值: {best_value:.6f}')
            
            # 设置坐标轴标签
            canvas.axes.set_xlabel('迭代次数', fontsize=16, fontweight='bold')
            canvas.axes.set_ylabel('目标函数值', fontsize=16, fontweight='bold')
            
            # 设置标题
            canvas.axes.set_title('优化收敛曲线', fontsize=18, fontweight='bold', pad=20)
            
            # 添加网格
            canvas.axes.grid(True, alpha=0.3, linestyle='--')
            
            # 添加图例
            canvas.axes.legend(loc='upper right')
            
            # 设置科学计数法格式（如果数值很小）
            if values and min(values) < 1e-3:
                canvas.axes.ticklabel_format(style='scientific', axis='y', scilimits=(0,0))
        else:
            # 显示等待状态
            canvas.axes.text(0.5, 0.5, '等待优化数据...', 
                           transform=canvas.axes.transAxes,
                           ha='center', va='center', fontsize=16,
                           bbox=dict(boxstyle='round,pad=0.5', facecolor='lightgray', alpha=0.7))
            canvas.axes.set_title('优化进程监控', fontsize=18, fontweight='bold', pad=20)
        
        # 设置坐标轴刻度和边框
        canvas.axes.tick_params(axis='both', which='major', labelsize=14, width=2, length=6)
        for spine in canvas.axes.spines.values():
            spine.set_linewidth(2)
        
        # 优化布局
        canvas.safe_tight_layout(pad=2.0)
        canvas.draw()

    def update_optimization_plot_with_components(self, loss_data):
        """更新优化进程图 - 支持训练/验证损失分离显示"""
        try:
            #  减少刷新频率，防止程序崩溃
            current_iter = len(loss_data.get('iterations', []))
            if hasattr(self, 'last_refresh_iter'):
                # 只在特定条件下刷新：
                # 1. 前10次迭代每次都刷新（观察初期变化）
                # 2. 之后每5次迭代刷新一次
                # 3. 每100次迭代强制刷新一次
                if (current_iter <= 10 or 
                    current_iter % 5 == 0 or 
                    current_iter % 100 == 0 or
                    current_iter - self.last_refresh_iter >= 10):
                    self.last_refresh_iter = current_iter
                else:
                    # 跳过这次刷新，减少CPU和内存压力
                    return
            else:
                self.last_refresh_iter = current_iter
            
            print(f" 开始绘制优化图表（第{current_iter}次迭代）")
            
            canvas = self.canvases["optimization"]
            canvas.axes.clear()
            
            # 设置论文级别的字体参数
            plt.rcParams.update({
                'font.size': 14,
                'axes.titlesize': 18,
                'axes.labelsize': 16,
                'xtick.labelsize': 14,
                'ytick.labelsize': 14,
                'legend.fontsize': 14,
                'font.family': 'sans-serif',
                'font.sans-serif': ['Microsoft YaHei', 'Arial Unicode MS', 'DejaVu Sans', 'Arial'],
                'axes.unicode_minus': False,
            })
            
            iterations = loss_data.get('iterations', [])
            
            if iterations and len(iterations) > 0:
                print(f" 绘制迭代范围：{min(iterations)} - {max(iterations)}")
                
                # 绘制训练损失曲线
                if 'train_loss' in loss_data and loss_data['train_loss']:
                    train_losses = loss_data['train_loss']
                    valid_train = [x for x in train_losses if x > 0]  # 过滤无效数据
                    if valid_train:
                        print(f" 绘制训练损失：{len(valid_train)}个点，范围{min(valid_train):.6f}-{max(valid_train):.6f}")
                        canvas.axes.plot(iterations[-len(train_losses):], train_losses, 
                                       'o-', linewidth=3, markersize=6,
                                       color='#1f77b4', markerfacecolor='#1f77b4', 
                                       markeredgecolor='white', markeredgewidth=1,
                                       label='训练损失 (Training Loss)', alpha=0.8)
                
                # 绘制验证损失曲线
                if 'val_loss' in loss_data and loss_data['val_loss']:
                    val_losses = loss_data['val_loss']
                    valid_val = [x for x in val_losses if x > 0]  # 过滤无效数据
                    if valid_val:
                        print(f" 绘制验证损失：{len(valid_val)}个点，范围{min(valid_val):.6f}-{max(valid_val):.6f}")
                        canvas.axes.plot(iterations[-len(val_losses):], val_losses, 
                                       's-', linewidth=3, markersize=6,
                                       color='#ff7f0e', markerfacecolor='#ff7f0e', 
                                       markeredgecolor='white', markeredgewidth=1,
                                       label='验证损失 (Validation Loss)', alpha=0.8)
                
                # 绘制总损失曲线（可选）
                if 'total_loss' in loss_data and loss_data['total_loss']:
                    total_losses = loss_data['total_loss']
                    valid_total = [x for x in total_losses if x > 0]  # 过滤无效数据
                    if valid_total:
                        print(f" 绘制总损失：{len(valid_total)}个点，范围{min(valid_total):.6f}-{max(valid_total):.6f}")
                        canvas.axes.plot(iterations[-len(total_losses):], total_losses, 
                                       '^-', linewidth=2, markersize=5,
                                       color='#2ca02c', markerfacecolor='#2ca02c', 
                                       markeredgecolor='white', markeredgewidth=1,
                                       label='总损失 (Total Loss)', alpha=0.6)
                
                # 添加最佳值标记
                all_losses = []
                if 'train_loss' in loss_data and loss_data['train_loss']:
                    all_losses.extend([x for x in loss_data['train_loss'] if x > 0])
                if 'val_loss' in loss_data and loss_data['val_loss']:
                    all_losses.extend([x for x in loss_data['val_loss'] if x > 0])
                
                if all_losses:
                    best_loss = min(all_losses)
                    canvas.axes.axhline(y=best_loss, color='red', linestyle='--', 
                                      linewidth=2, alpha=0.7, 
                                      label=f'最优值: {best_loss:.6f}')
                    print(f" 标记最佳损失值：{best_loss:.6f}")
                
                # 设置坐标轴标签和标题
                canvas.axes.set_xlabel('迭代次数 (Iteration)', fontsize=16, fontweight='bold')
                canvas.axes.set_ylabel('损失值 (Loss)', fontsize=16, fontweight='bold')
                canvas.axes.set_title('训练与验证损失对比 (Training vs Validation Loss)', 
                                    fontsize=18, fontweight='bold', pad=20)
                
                # 添加网格和图例
                canvas.axes.grid(True, alpha=0.3, linestyle='--')
                canvas.axes.legend(loc='upper right', framealpha=0.9)
                
                # 设置科学计数法格式（如果数值很大）
                if all_losses and max(all_losses) > 1000:
                    canvas.axes.ticklabel_format(style='scientific', axis='y', scilimits=(0,0))
                
                print(" 图表绘制完成")
                
            else:
                print(" 没有有效的迭代数据，显示等待状态")
                # 显示等待状态
                canvas.axes.text(0.5, 0.5, '等待优化数据...', 
                               transform=canvas.axes.transAxes,
                               ha='center', va='center', fontsize=16,
                               bbox=dict(boxstyle='round,pad=0.5', facecolor='lightgray', alpha=0.7))
                canvas.axes.set_title('优化进程监控', fontsize=18, fontweight='bold', pad=20)
            
            # 设置坐标轴刻度和边框
            canvas.axes.tick_params(axis='both', which='major', labelsize=14, width=2, length=6)
            for spine in canvas.axes.spines.values():
                spine.set_linewidth(2)
            
            #  重要：使用更安全的布局刷新方式
            try:
                canvas.safe_tight_layout(pad=2.0)
            except Exception as layout_e:
                print(f" 布局调整失败，跳过: {layout_e}")
            
            #  重要：使用更温和的绘制方式，避免频繁flush
            canvas.draw()
            
            # 只在重要节点强制切换选项卡
            if current_iter <= 5 or current_iter % 50 == 0:
                self.tab_widget.setCurrentIndex(0)
                print(" 已切换到优化进度选项卡")
            
            # 同时更新AI洞察（降低频率）
            if hasattr(self, 'iterations_list') and len(self.iterations_list) % 25 == 0:
                self.update_ai_insights()
            
        except Exception as e:
            print(f"❌ 绘制优化图表时出错: {e}")
            # 不要打印完整的traceback，减少输出量

    def update_parameter_space(self, search_path, current_best=None):
        """更新参数空间图"""
        canvas = self.canvases["parameter_space"]
        canvas.axes.clear()
        
        # 设置论文级别的字体参数
        plt.rcParams.update({
            'font.size': 14,
            'axes.titlesize': 18,
            'axes.labelsize': 16,
            'xtick.labelsize': 14,
            'ytick.labelsize': 14,
            'legend.fontsize': 14,
            'font.family': 'serif',
        })
        
        if search_path and len(search_path) > 0:
            try:
                # 获取参数名称（取前两个主要参数）
                first_point = search_path[0]
                if isinstance(first_point, dict):
                    param_names = list(first_point.keys())[:2]  # 只取前两个参数
                else:
                    print("警告：搜索路径格式不正确")
                    return
                
                if len(param_names) >= 2:
                    # 提取参数值
                    x_values = []
                    y_values = []
                    
                    for point in search_path:
                        if isinstance(point, dict):
                            x_val = point.get(param_names[0], 0)
                            y_val = point.get(param_names[1], 0)
                            x_values.append(float(x_val))
                            y_values.append(float(y_val))
                    
                    if len(x_values) > 0 and len(y_values) > 0:
                        # 绘制搜索路径
                        canvas.axes.plot(x_values, y_values, 'b-', linewidth=2, alpha=0.7, label='搜索轨迹')
                        
                        # 绘制搜索点
                        colors = plt.cm.viridis(np.linspace(0, 1, len(x_values)))
                        scatter = canvas.axes.scatter(x_values, y_values, c=colors, s=50, 
                                                    edgecolors='white', linewidth=1, 
                                                    label='搜索点', zorder=5)
                        
                        # 标记起始点
                        canvas.axes.scatter(x_values[0], y_values[0], c='green', s=150, 
                                          marker='s', edgecolors='black', linewidth=2,
                                          label='起始点', zorder=10)
                        
                        # 标记最新点
                        canvas.axes.scatter(x_values[-1], y_values[-1], c='red', s=150, 
                                          marker='o', edgecolors='black', linewidth=2,
                                          label='当前点', zorder=10)
                        
                        # 标记当前最优点
                        if current_best and isinstance(current_best, dict):
                            best_x = current_best.get(param_names[0], x_values[-1])
                            best_y = current_best.get(param_names[1], y_values[-1])
                            canvas.axes.scatter([best_x], [best_y], c='gold', s=200, 
                                              marker='*', edgecolors='black', linewidth=2,
                                              label='最优点', zorder=15)
                        
                        # 添加轨迹方向箭头
                        if len(x_values) > 5:
                            # 每5个点添加一个箭头
                            for i in range(5, len(x_values), 5):
                                dx = x_values[i] - x_values[i-1]
                                dy = y_values[i] - y_values[i-1]
                                if abs(dx) > 1e-10 or abs(dy) > 1e-10:  # 避免零向量
                                    canvas.axes.arrow(x_values[i-1], y_values[i-1], dx*0.3, dy*0.3,
                                                    head_width=0.02, head_length=0.03, 
                                                    fc='blue', ec='blue', alpha=0.6)
                        
                        # 设置坐标轴标签
                        canvas.axes.set_xlabel(f'{param_names[0]}', fontsize=16, fontweight='bold')
                        canvas.axes.set_ylabel(f'{param_names[1]}', fontsize=16, fontweight='bold')
                        
                        # 设置标题（包含统计信息）
                        title = f'参数空间搜索轨迹 ({len(search_path)}个点)'
                        canvas.axes.set_title(title, fontsize=18, fontweight='bold', pad=20)
                        
                        # 添加网格和图例
                        canvas.axes.grid(True, alpha=0.3, linestyle='--')
                        canvas.axes.legend(loc='best')
                        
                        # 设置坐标轴等比例（如果范围相近）
                        x_range = max(x_values) - min(x_values)
                        y_range = max(y_values) - min(y_values)
                        if x_range > 0 and y_range > 0 and abs(x_range - y_range) / max(x_range, y_range) < 2:
                            canvas.axes.set_aspect('equal', adjustable='box')
                        
                        print(f" 参数空间更新成功：{param_names[0]} vs {param_names[1]}，{len(search_path)}个点")
                    
                elif len(param_names) == 1:
                    # 单参数情况，显示参数随迭代次数的变化
                    param_values = [point.get(param_names[0], 0) for point in search_path]
                    iterations = list(range(1, len(param_values) + 1))
                    
                    canvas.axes.plot(iterations, param_values, 'bo-', linewidth=2, markersize=6)
                    canvas.axes.set_xlabel('迭代次数', fontsize=16, fontweight='bold')
                    canvas.axes.set_ylabel(f'{param_names[0]}', fontsize=16, fontweight='bold')
                    canvas.axes.set_title('单参数优化轨迹', fontsize=18, fontweight='bold', pad=20)
                    canvas.axes.grid(True, alpha=0.3, linestyle='--')
                
            except Exception as e:
                print(f" 参数空间更新出错: {e}")
                # 显示错误提示
                canvas.axes.text(0.5, 0.5, f'参数空间数据处理出错\n{str(e)}', 
                                transform=canvas.axes.transAxes,
                                ha='center', va='center', fontsize=14,
                                bbox=dict(boxstyle='round,pad=0.5', facecolor='lightyellow', alpha=0.8))
                canvas.axes.set_title('参数空间搜索轨迹', fontsize=18, fontweight='bold', pad=20)
        else:
            # 显示等待状态
            canvas.axes.text(0.5, 0.5, '等待参数搜索数据...\n\n开始优化后将显示：\n• 参数搜索轨迹\n• 起始点和当前点\n• 最优点标记', 
                           transform=canvas.axes.transAxes,
                           ha='center', va='center', fontsize=14,
                           bbox=dict(boxstyle='round,pad=0.5', facecolor='lightgray', alpha=0.7))
            canvas.axes.set_title('参数空间可视化', fontsize=18, fontweight='bold', pad=20)
        
        # 设置坐标轴格式
        canvas.axes.tick_params(axis='both', which='major', labelsize=14, width=2, length=6)
        for spine in canvas.axes.spines.values():
            spine.set_linewidth(2)
        
        canvas.safe_tight_layout(pad=2.0)
        canvas.draw()

    def update_pareto_front(self, pareto_points):
        """更新帕累托前沿图"""
        canvas = self.canvases["pareto"]
        canvas.axes.clear()
        
        # 设置论文级别的字体参数
        plt.rcParams.update({
            'font.size': 14,
            'axes.titlesize': 18,
            'axes.labelsize': 16,
            'xtick.labelsize': 14,
            'ytick.labelsize': 14,
            'legend.fontsize': 14,
            'font.family': 'sans-serif',
            'font.sans-serif': ['Microsoft YaHei', 'Arial Unicode MS', 'DejaVu Sans', 'Arial'],
            'axes.unicode_minus': False,
        })
        
        if pareto_points and len(pareto_points) > 0:
            # 提取目标函数值
            obj1_values = [sol['objective1'] for sol in pareto_points]
            obj2_values = [sol['objective2'] for sol in pareto_points]
            
            # 绘制帕累托前沿散点
            canvas.axes.scatter(obj1_values, obj2_values, s=120, c='#d62728', 
                              marker='o', alpha=0.8, edgecolors='black', 
                              linewidth=1.5, label='帕累托最优解', zorder=5)
            
            # 连接帕累托前沿点
            if len(obj1_values) > 1:
                sorted_indices = np.argsort(obj1_values)
                sorted_obj1 = [obj1_values[i] for i in sorted_indices]
                sorted_obj2 = [obj2_values[i] for i in sorted_indices]
                canvas.axes.plot(sorted_obj1, sorted_obj2, 'r--', linewidth=2, 
                               alpha=0.7, label='帕累托前沿')
            
            # 添加最优解标记
            if obj1_values and obj2_values:
                # 找到能量RMSE最小的点
                energy_best_idx = np.argmin(obj1_values)
                canvas.axes.scatter([obj1_values[energy_best_idx]], [obj2_values[energy_best_idx]], 
                                  c='blue', s=200, marker='*', edgecolors='black', 
                                  linewidth=2, label='能量最优', zorder=10)
                
                # 找到力RMSE最小的点
                force_best_idx = np.argmin(obj2_values)
                canvas.axes.scatter([obj1_values[force_best_idx]], [obj2_values[force_best_idx]], 
                                  c='green', s=200, marker='s', edgecolors='black', 
                                  linewidth=2, label='力最优', zorder=10)
            
            # 设置坐标轴标签
            canvas.axes.set_xlabel('能量 RMSE (kcal/mol)', fontsize=16, fontweight='bold')
            canvas.axes.set_ylabel('力 RMSE (kcal/mol/Å)', fontsize=16, fontweight='bold')
            
            # 设置标题
            canvas.axes.set_title(f'多目标优化帕累托前沿\n({len(pareto_points)} 个解)', 
                                fontsize=18, fontweight='bold', pad=20)
            
            # 添加网格和图例
            canvas.axes.grid(True, alpha=0.3, linestyle='--')
            canvas.axes.legend(loc='upper right', frameon=True, 
                             fancybox=True, shadow=True)
            
            # 添加统计信息文本框
            stats_text = f"解的数量: {len(pareto_points)}\n"
            stats_text += f"能量RMSE范围: {min(obj1_values):.4f} - {max(obj1_values):.4f}\n"
            stats_text += f"力RMSE范围: {min(obj2_values):.4f} - {max(obj2_values):.4f}"
            
            canvas.axes.text(0.02, 0.98, stats_text, transform=canvas.axes.transAxes,
                            fontsize=12, verticalalignment='top',
                            bbox=dict(boxstyle='round,pad=0.5', facecolor='lightyellow', alpha=0.8))
            
        else:
            # 显示等待状态
            canvas.axes.text(0.5, 0.5, '等待多目标优化结果...\n\n将显示：\n• 帕累托最优解集\n• 能量-力误差权衡\n• 最优解标记', 
                           transform=canvas.axes.transAxes,
                           ha='center', va='center', fontsize=14,
                           bbox=dict(boxstyle='round,pad=0.5', facecolor='lightgray', alpha=0.7))
            canvas.axes.set_title('多目标优化帕累托前沿', fontsize=18, fontweight='bold', pad=20)
        
        # 设置坐标轴格式
        canvas.axes.tick_params(axis='both', which='major', labelsize=14, width=2, length=6)
        for spine in canvas.axes.spines.values():
            spine.set_linewidth(2)
        
        canvas.safe_tight_layout(pad=2.0)
        canvas.draw()

    def display_figure(self, fig, tab_name="pareto"):
        """显示外部创建的图形（简化版）

        Args:
            fig (matplotlib.figure.Figure): 要显示的图形
            tab_name (str): 目标选项卡名称
        """
        if tab_name not in self.canvases:
            tab_name = "pareto"  # 默认使用帕累托选项卡

        canvas = self.canvases[tab_name]

        # 简化的图形复制：直接复制第一个子图的内容
        if fig.axes:
            ax = fig.axes[0]
            canvas.axes.clear()

            # 复制基本绘图元素
            for line in ax.get_lines():
                x, y = line.get_data()
                canvas.axes.plot(x, y,
                               color=line.get_color(),
                               linestyle=line.get_linestyle(),
                               marker=line.get_marker(),
                               label=line.get_label())

            # 复制标签和标题
            canvas.axes.set_xlabel(ax.get_xlabel())
            canvas.axes.set_ylabel(ax.get_ylabel())
            canvas.axes.set_title(f"AI增强 - {ax.get_title()}")

            if ax.get_legend():
                canvas.axes.legend()

        # 重绘
        canvas.safe_tight_layout()
        canvas.draw()

        # 切换到对应选项卡
        tab_index = list(self.canvases.keys()).index(tab_name)
        self.tab_widget.setCurrentIndex(tab_index)

    def export_current_figure(self):
        """导出当前图表"""
        # 获取当前选项卡索引
        current_index = self.tab_widget.currentIndex()

        # 确定当前画布和默认文件名
        tab_names = ["optimization", "parameter_space", "pareto", "ai_insights"]
        default_names = ["optimization_process.png", "parameter_space.png", "pareto_front.png", "ai_insights.png"]

        if current_index < len(tab_names):
            canvas_key = tab_names[current_index]
            default_name = default_names[current_index]

            if canvas_key in self.canvases:
                canvas = self.canvases[canvas_key]
            else:
                return
        else:
            return

        # 打开保存文件对话框
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出图表", default_name,
            "PNG图像 (*.png);;JPEG图像 (*.jpg);;PDF文件 (*.pdf);;SVG图像 (*.svg);;所有文件 (*.*)"
        )

        if file_path:
            # 保存图形
            canvas.fig.savefig(file_path, dpi=300, bbox_inches='tight')

    def update_structures(self, structures):
        """更新结构列表

        Args:
            structures (dict): 分子结构字典，键为数据集名称，值为结构列表
        """
        self.structures = structures

        # 获取参数空间画布
        canvas = self.canvases['parameter_space']

        # 清除当前图形
        canvas.axes.clear()

        # 如果有结构，绘制第一个数据集的第一个结构的示意图
        if structures:
            # 获取第一个数据集的结构
            first_dataset = next(iter(structures.values()))
            if first_dataset:  # 确保数据集不为空
                structure = first_dataset[0] if isinstance(first_dataset, list) else first_dataset

                # 检查结构是否包含必要的数据
                if 'coordinates' in structure and 'atom_types' in structure:
                    coords = structure['coordinates']
                    atom_types = structure['atom_types']

                    # 创建3D散点图
                    canvas.axes = canvas.fig.add_subplot(111, projection='3d')
                    scatter = canvas.axes.scatter(
                        coords[:, 0], coords[:, 1], coords[:, 2],
                        c=atom_types, cmap='viridis'
                    )

                    # 添加颜色条
                    canvas.fig.colorbar(scatter, label='原子类型')

                    # 设置轴标签
                    canvas.axes.set_xlabel('X (Å)')
                    canvas.axes.set_ylabel('Y (Å)')
                    canvas.axes.set_zlabel('Z (Å)')

                    # 设置标题
                    dataset_name = next(iter(structures.keys()))
                    canvas.axes.set_title(f'分子结构示意图 - {dataset_name}')

                    # 设置相等的坐标轴比例
                    try:
                        canvas.axes.set_box_aspect([1,1,1])
                    except:
                        pass  # 某些matplotlib版本可能不支持此方法
                else:
                    # 如果没有坐标数据，显示提示信息
                    canvas.axes.text(0.5, 0.5, "结构数据加载成功\n但缺少坐标信息",
                                    ha='center', va='center', transform=canvas.axes.transAxes)

        # 重绘（安全处理布局）
        canvas.safe_tight_layout()
        canvas.draw()

        # 切换到参数空间选项卡
        self.tab_widget.setCurrentIndex(1)

    def update_optimization_progress(self, iteration, loss_data, params):
        """更新优化进度显示 - 主窗口调用的接口方法"""
        try:
            print(f" 可视化面板接收到优化进度更新：迭代{iteration}, 损失数据类型{type(loss_data)}")
            
            # 确保有足够的迭代数据来绘图
            if not hasattr(self, 'iterations_list'):
                self.iterations_list = []
                self.total_losses = []
                self.train_losses = []
                self.val_losses = []
            
            # 添加当前迭代数据
            self.iterations_list.append(iteration)
            self.total_losses.append(loss_data.get('total', loss_data.get('loss', 0)))
            self.train_losses.append(loss_data.get('train', 0))
            self.val_losses.append(loss_data.get('validation', 0))
            
            # 构建完整的损失数据格式
            complete_loss_data = {
                'iterations': self.iterations_list.copy(),
                'total_loss': self.total_losses.copy(),
                'train_loss': self.train_losses.copy(),
                'val_loss': self.val_losses.copy()
            }
            
            # 调用实际的绘图方法
            self.update_optimization_plot_with_components(complete_loss_data)
            
            print(f" 优化进度图表已更新，迭代{iteration}，数据点{len(self.iterations_list)}个")
            
        except Exception as e:
            print(f" 更新优化进度显示时出错: {e}")
            import traceback
            traceback.print_exc()

    def update_parameter_space(self, search_path, current_best=None):
        """更新参数空间可视化

        Args:
            search_path (list): 搜索路径点列表，每个点是一个参数值字典
            current_best (dict, optional): 当前最优点
        """
        # 获取参数空间画布
        canvas = self.canvases['parameter_space']

        # 清除当前图形
        canvas.axes.clear()

        # 提取参数名称
        param_names = list(search_path[0].keys())

        if len(param_names) == 2:
            # 2D参数空间
            x = [point[param_names[0]] for point in search_path]
            y = [point[param_names[1]] for point in search_path]

            # 绘制搜索路径
            canvas.axes.plot(x, y, 'b-', alpha=0.5, label='搜索路径')
            canvas.axes.scatter(x, y, c='blue', s=20, alpha=0.5)

            # 标记当前最优点
            if current_best:
                canvas.axes.scatter(
                    current_best[param_names[0]],
                    current_best[param_names[1]],
                    c='red', s=100, marker='*', label='当前最优'
                )

            # 设置标签
            canvas.axes.set_xlabel(param_names[0])
            canvas.axes.set_ylabel(param_names[1])

        elif len(param_names) == 3:
            # 3D参数空间
            canvas.axes = canvas.fig.add_subplot(111, projection='3d')

            x = [point[param_names[0]] for point in search_path]
            y = [point[param_names[1]] for point in search_path]
            z = [point[param_names[2]] for point in search_path]

            # 绘制搜索路径
            canvas.axes.plot3D(x, y, z, 'b-', alpha=0.5, label='搜索路径')
            canvas.axes.scatter3D(x, y, z, c='blue', s=20, alpha=0.5)

            # 标记当前最优点
            if current_best:
                canvas.axes.scatter3D(
                    current_best[param_names[0]],
                    current_best[param_names[1]],
                    current_best[param_names[2]],
                    c='red', s=100, marker='*', label='当前最优'
                )

            # 设置标签
            canvas.axes.set_xlabel(param_names[0])
            canvas.axes.set_ylabel(param_names[1])
            canvas.axes.set_zlabel(param_names[2])

        else:
            # 超过3个参数时，选择前两个主要参数
            # 判断是否为3D坐标轴
            if hasattr(canvas.axes, 'zaxis'):
                canvas.axes.text(0.5, 0.5, 0.5, "参数空间维度过高\n显示前两个主要参数",
                                transform=canvas.axes.transAxes,
                                ha='center', va='center')
            else:
                canvas.axes.text(0.5, 0.5, "参数空间维度过高\n显示前两个主要参数",
                                transform=canvas.axes.transAxes,
                                ha='center', va='center')

        # 添加图例
        canvas.axes.legend()

        # 设置标题
        canvas.axes.set_title("参数空间搜索路径")

        # 重绘
        canvas.safe_tight_layout()
        canvas.draw()

        # 切换到参数空间选项卡
        self.tab_widget.setCurrentIndex(1)

    def update_pareto_front_simple(self, pareto_points):
        """更新帕累托前沿可视化 - 简化版本

        Args:
            pareto_points (list): 帕累托最优解列表，每个点是一个(obj1, obj2)元组
        """
        # 获取帕累托前沿画布
        canvas = self.canvases['pareto']

        # 清除当前图形
        canvas.axes.clear()

        # 提取目标函数值
        obj1_values = [point[0] for point in pareto_points]
        obj2_values = [point[1] for point in pareto_points]

        # 绘制散点图
        canvas.axes.scatter(
            obj1_values, obj2_values,
            c='red', s=50, label='帕累托最优解'
        )

        # 按第一个目标函数值排序并连线
        sorted_indices = np.argsort(obj1_values)
        sorted_obj1 = [obj1_values[i] for i in sorted_indices]
        sorted_obj2 = [obj2_values[i] for i in sorted_indices]
        canvas.axes.plot(
            sorted_obj1, sorted_obj2,
            'r--', alpha=0.5, label='帕累托前沿'
        )

        # 设置标签
        canvas.axes.set_xlabel('目标1: 能量RMSE (kcal/mol)')
        canvas.axes.set_ylabel('目标2: 力RMSE (kcal/mol/Å)')
        canvas.axes.set_title('多目标优化帕累托前沿')

        # 添加图例
        canvas.axes.legend()

        # 重绘
        canvas.safe_tight_layout()
        canvas.draw()

        # 切换到帕累托前沿选项卡
        self.tab_widget.setCurrentIndex(2)
    
    def plot_pareto_front(self):
        """绘制帕累托前沿 - 兼容方法"""
        # 如果有数据就绘制，否则显示提示
        canvas = self.canvases["pareto"]
        canvas.axes.clear()
        
        canvas.axes.text(0.5, 0.5, '请先运行多目标优化\n以生成帕累托前沿数据', 
                        transform=canvas.axes.transAxes,
                        ha='center', va='center', fontsize=14,
                        bbox=dict(boxstyle='round,pad=0.5', facecolor='lightblue', alpha=0.7))
        canvas.axes.set_title('帕累托前沿图', fontsize=18, fontweight='bold')
        canvas.draw()
        
        print(" 帕累托前沿绘制方法被调用，等待多目标优化数据")

    def save_all_figures(self, output_dir):
        """保存所有图表

        Args:
            output_dir (str): 输出目录
        """
        # 保存优化进程图
        self.canvases['optimization'].fig.savefig(
            os.path.join(output_dir, "optimization_process.png"),
            dpi=300, bbox_inches='tight'
        )

        # 保存参数空间图
        self.canvases['parameter_space'].fig.savefig(
            os.path.join(output_dir, "parameter_space.png"),
            dpi=300, bbox_inches='tight'
        )

        # 保存帕累托前沿图
        self.canvases['pareto'].fig.savefig(
            os.path.join(output_dir, "pareto_front.png"),
            dpi=300, bbox_inches='tight'
        )



    def create_ai_insights_tab(self):
        """创建AI洞察选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 创建滚动区域
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        
        # 洞察内容区域
        self.insights_content = QWidget()
        self.insights_layout = QVBoxLayout(self.insights_content)
        
        # 默认显示
        self.show_default_insights()
        
        scroll.setWidget(self.insights_content)
        layout.addWidget(scroll)
        
        # 添加刷新按钮
        refresh_btn = QPushButton(" 刷新AI洞察")
        refresh_btn.clicked.connect(self.update_ai_insights)
        layout.addWidget(refresh_btn)
        
        return widget
    
    def show_default_insights(self):
        """显示默认的AI洞察内容"""
        # 清除现有内容
        for i in reversed(range(self.insights_layout.count())):
            self.insights_layout.itemAt(i).widget().setParent(None)
        
        # 标题
        title = QLabel(" AI优化洞察分析")
        title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #e8f4fd, stop:1 #d4edda);
                border-radius: 8px;
                margin: 5px;
            }
        """)
        self.insights_layout.addWidget(title)
        
        # 等待分析状态
        waiting_label = QLabel(" 等待优化数据进行AI分析...")
        waiting_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                padding: 20px;
                background-color: #f8f9fa;
                border: 2px dashed #dee2e6;
                border-radius: 8px;
                color: #6c757d;
                text-align: center;
            }
        """)
        self.insights_layout.addWidget(waiting_label)
        
        # 功能说明
        features_label = QLabel("""
        <h3>AI洞察功能：</h3>
        <ul>
            <li>📊 <b>优化趋势分析</b> - 收敛速度和稳定性评估</li>
            <li>🎯 <b>参数重要性排名</b> - 识别关键参数</li>
            <li>⚡ <b>优化策略建议</b> - 改进优化算法建议</li>
            <li>🔍 <b>异常检测</b> - 识别优化过程中的异常行为</li>
            <li>📈 <b>收敛预测</b> - 预测收敛时间和最终精度</li>
        </ul>
        """)
        features_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                padding: 15px;
                background-color: #ffffff;
                border: 1px solid #e9ecef;
                border-radius: 8px;
                color: #495057;
            }
        """)
        self.insights_layout.addWidget(features_label)
    
    def update_ai_insights(self):
        """更新AI洞察内容"""
        try:
            # 检查是否有优化数据
            if not hasattr(self, 'iterations_list') or not self.iterations_list:
                self.show_default_insights()
                return
            
            # 清除现有内容
            for i in reversed(range(self.insights_layout.count())):
                self.insights_layout.itemAt(i).widget().setParent(None)
            
            # 标题
            title = QLabel(" AI优化洞察分析")
            title.setStyleSheet("""
                QLabel {
                    font-size: 18px;
                    font-weight: bold;
                    color: #2c3e50;
                    padding: 10px;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #e8f4fd, stop:1 #d4edda);
                    border-radius: 8px;
                    margin: 5px;
                }
            """)
            self.insights_layout.addWidget(title)
            
            # 进行AI分析
            insights = self.generate_ai_insights()
            
            # 显示分析结果
            for insight in insights:
                insight_widget = self.create_insight_widget(insight)
                self.insights_layout.addWidget(insight_widget)
            
            print(" AI洞察已更新")
            
        except Exception as e:
            print(f" 更新AI洞察时出错: {e}")
    
    def generate_ai_insights(self):
        """生成AI洞察分析"""
        insights = []
        
        if not hasattr(self, 'total_losses') or not self.total_losses:
            return insights
        
        # 1. 收敛趋势分析
        if len(self.total_losses) > 10:
            recent_losses = self.total_losses[-10:]
            early_losses = self.total_losses[:10]
            
            improvement = (np.mean(early_losses) - np.mean(recent_losses)) / np.mean(early_losses) * 100
            convergence_rate = self.calculate_convergence_rate()
            
            if improvement > 5:
                trend = " 快速收敛"
                trend_desc = f"损失已改善 {improvement:.1f}%，收敛速度: {convergence_rate:.3f}/迭代"
                color = "#28a745"
            elif improvement > 1:
                trend = " 稳定改善"
                trend_desc = f"损失已改善 {improvement:.1f}%，保持稳定收敛"
                color = "#17a2b8"
            else:
                trend = " 收敛缓慢"
                trend_desc = f"损失改善较小 ({improvement:.1f}%)，可能需要调整策略"
                color = "#ffc107"
            
            insights.append({
                'title': f" 收敛趋势：{trend}",
                'content': trend_desc,
                'color': color,
                'icon': '📊'
            })
        
        # 2. 参数重要性分析
        if hasattr(self, 'search_path_history') and len(self.search_path_history) > 5:
            param_importance = self.analyze_parameter_importance()
            
            importance_text = "参数重要性排名：\n"
            for i, (param, importance) in enumerate(param_importance[:5]):
                importance_text += f"{i+1}. {param}: {importance:.3f}\n"
            
            insights.append({
                'title': " 关键参数识别",
                'content': importance_text,
                'color': "#6f42c1",
                'icon': '🎯'
            })
        
        # 3. 优化策略建议
        strategy_suggestion = self.generate_strategy_suggestion()
        insights.append({
            'title': " 优化策略建议",
            'content': strategy_suggestion,
            'color': "#fd7e14",
            'icon': '⚡'
        })
        
        # 4. 异常检测
        anomalies = self.detect_anomalies()
        if anomalies:
            insights.append({
                'title': " 异常检测",
                'content': anomalies,
                'color': "#dc3545",
                'icon': '🔍'
            })
        
        # 5. 收敛预测
        prediction = self.predict_convergence()
        insights.append({
            'title': " 收敛预测",
            'content': prediction,
            'color': "#20c997",
            'icon': '📈'
        })
        
        return insights
    
    def calculate_convergence_rate(self):
        """计算收敛速度"""
        if len(self.total_losses) < 5:
            return 0.0
        
        # 计算最近几次的平均改善率
        recent_changes = []
        for i in range(1, min(10, len(self.total_losses))):
            if self.total_losses[-i-1] > 0:
                change = (self.total_losses[-i-1] - self.total_losses[-i]) / self.total_losses[-i-1]
                recent_changes.append(abs(change))
        
        return np.mean(recent_changes) if recent_changes else 0.0
    
    def analyze_parameter_importance(self):
        """分析参数重要性"""
        if not hasattr(self, 'search_path_history') or len(self.search_path_history) < 2:
            return []
        
        # 计算每个参数的变化幅度
        param_importance = {}
        first_params = self.search_path_history[0]
        
        for param_name in first_params.keys():
            values = [step.get(param_name, 0) for step in self.search_path_history]
            if len(values) > 1:
                # 计算标准差作为重要性指标
                importance = np.std(values) / (np.mean(np.abs(values)) + 1e-10)
                param_importance[param_name] = importance
        
        # 按重要性排序
        return sorted(param_importance.items(), key=lambda x: x[1], reverse=True)
    
    def generate_strategy_suggestion(self):
        """生成优化策略建议"""
        if not hasattr(self, 'total_losses') or len(self.total_losses) < 10:
            return "数据不足，需要更多迭代来分析"
        
        # 分析损失变化模式
        recent_variance = np.var(self.total_losses[-20:]) if len(self.total_losses) >= 20 else np.var(self.total_losses)
        overall_trend = (self.total_losses[0] - self.total_losses[-1]) / self.total_losses[0]
        
        suggestions = []
        
        if recent_variance > np.var(self.total_losses) * 2:
            suggestions.append("• 降低学习率，减少优化过程中的震荡")
        
        if overall_trend < 0.01:
            suggestions.append("• 尝试不同的优化算法（如遗传算法或模拟退火）")
            suggestions.append("• 增加扰动强度，帮助跳出局部最优")
        
        if len(suggestions) == 0:
            suggestions.append("• 当前优化策略表现良好，建议继续")
            suggestions.append("• 可以考虑增加迭代次数以获得更好的结果")
        
        return "\n".join(suggestions)
    
    def detect_anomalies(self):
        """检测优化过程中的异常"""
        if not hasattr(self, 'total_losses') or len(self.total_losses) < 10:
            return None
        
        anomalies = []
        
        # 检测损失突然增大
        for i in range(1, len(self.total_losses)):
            if i > 0 and self.total_losses[i] > self.total_losses[i-1] * 1.5:
                anomalies.append(f"• 第{i}次迭代损失突然增大 ({self.total_losses[i-1]:.3f} → {self.total_losses[i]:.3f})")
        
        # 检测损失停滞
        if len(self.total_losses) > 20:
            recent_20 = self.total_losses[-20:]
            if np.var(recent_20) < np.var(self.total_losses) * 0.1:
                anomalies.append("• 最近20次迭代损失变化很小，可能陷入局部最优")
        
        return "\n".join(anomalies) if anomalies else None
    
    def predict_convergence(self):
        """预测收敛情况"""
        if not hasattr(self, 'total_losses') or len(self.total_losses) < 5:
            return "数据不足，无法进行预测"
        
        # 简单的线性拟合预测
        iterations = np.array(range(len(self.total_losses)))
        losses = np.array(self.total_losses)
        
        # 计算趋势
        if len(losses) > 10:
            recent_trend = np.polyfit(iterations[-10:], losses[-10:], 1)[0]
            current_loss = losses[-1]
            
            if abs(recent_trend) < 1e-6:
                return f"已基本收敛，当前损失: {current_loss:.6f}"
            elif recent_trend < 0:
                # 预测何时达到目标精度
                target_loss = current_loss * 0.9  # 再改善10%
                estimated_iters = int(abs((target_loss - current_loss) / recent_trend))
                return f"预计还需 {estimated_iters} 次迭代达到 {target_loss:.6f}"
            else:
                return "当前呈上升趋势，建议检查优化策略"
        
        return "需要更多数据进行准确预测"
    
    def create_insight_widget(self, insight):
        """创建洞察卡片"""
        widget = QWidget()
        widget.setStyleSheet(f"""
            QWidget {{
                background-color: {insight['color']}15;
                border: 2px solid {insight['color']};
                border-radius: 8px;
                margin: 5px;
            }}
        """)
        
        layout = QVBoxLayout(widget)
        
        # 标题
        title = QLabel(insight['title'])
        title.setStyleSheet(f"""
            QLabel {{
                font-size: 14px;
                font-weight: bold;
                color: {insight['color']};
                padding: 8px;
            }}
        """)
        layout.addWidget(title)
        
        # 内容
        content = QLabel(insight['content'])
        content.setStyleSheet("""
            QLabel {
                font-size: 12px;
                padding: 8px;
                color: #495057;
                line-height: 1.4;
            }
        """)
        content.setWordWrap(True)
        layout.addWidget(content)
        
        return widget